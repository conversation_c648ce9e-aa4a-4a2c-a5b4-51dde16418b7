const abdmService = require('./src/services/abdm-service')

async function testAbdmDirectly() {
  console.log('🧪 Testing ABDM Service Directly...')

  try {
    console.log('\n1. Testing Session Token Generation...')
    const sessionResult = await abdmService.getSessionToken()
    console.log(
      '✅ Session Token:',
      sessionResult ? 'Generated Successfully' : 'Failed',
    )

    console.log('\n2. Testing Mobile ABHA Initiation...')
    const mobileResult = await abdmService.initiateAbhaCreationByMobile(
      '7034303333',
    )
    console.log('📱 Mobile Result:', JSON.stringify(mobileResult, null, 2))

    if (mobileResult.success) {
      console.log('\n3. Testing Mobile OTP Verification (with dummy OTP)...')
      const otpResult = await abdmService.verifyMobileOtpForAbhaCreation(
        mobileResult.txnId,
        '123456',
      )
      console.log('🔐 OTP Result:', JSON.stringify(otpResult, null, 2))
    }

    console.log('\n4. Testing Aadhaar ABHA Initiation...')
    const aadhaarResult = await abdmService.initiateAbhaCreationByAadhaar(
      '123456789012',
    )
    console.log('🆔 Aadhaar Result:', JSON.stringify(aadhaarResult, null, 2))
  } catch (error) {
    console.error('❌ Error testing ABDM service:', error.message)
    console.error('Details:', error.response?.data || error)
  }
}

// Run the test
testAbdmDirectly()
  .then(() => {
    console.log('\n🎉 ABDM Direct Test Completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Test failed:', error)
    process.exit(1)
  })
