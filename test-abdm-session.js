/**
 * Test ABDM Session Token Generation
 * Quick test to verify session token generation is working
 */

const abdmService = require('./src/services/abdm-service')

async function testSessionGeneration() {
  console.log('🔐 Testing ABDM Session Token Generation...\n')
  
  try {
    console.log('Configuration:')
    console.log('- Base URL:', abdmService.baseUrl)
    console.log('- Auth URL:', abdmService.authUrl)
    console.log('- Client ID:', abdmService.clientId)
    console.log('- Client Secret:', abdmService.clientSecret ? '***configured***' : 'NOT SET')
    console.log('')

    console.log('Attempting to generate session token...')
    const token = await abdmService.getSessionToken()
    
    if (token) {
      console.log('✅ Session token generated successfully!')
      console.log('Token length:', token.length)
      console.log('Token preview:', token.substring(0, 20) + '...')
      
      // Test if we can use the cached token
      console.log('\nTesting cached token...')
      const cachedToken = await abdmService.getSessionToken()
      
      if (cachedToken === token) {
        console.log('✅ Token caching is working correctly')
      } else {
        console.log('⚠️  Token caching might not be working as expected')
      }
      
      return true
    } else {
      console.log('❌ Session token generation failed - no token returned')
      return false
    }
    
  } catch (error) {
    console.log('❌ Session token generation failed')
    console.log('Error:', error.message)
    
    if (error.message.includes('404')) {
      console.log('\n💡 Troubleshooting suggestions:')
      console.log('1. Check if the ABDM sandbox is accessible')
      console.log('2. Verify the client ID and secret are correct')
      console.log('3. Check if the API endpoints have changed')
      console.log('4. Try accessing the ABDM sandbox documentation')
    }
    
    return false
  }
}

async function testMobileInitiation() {
  console.log('\n📱 Testing Mobile ABHA Initiation...\n')
  
  try {
    const result = await abdmService.initiateAbhaCreationByMobile('7034303333')
    
    if (result.success) {
      console.log('✅ Mobile ABHA initiation successful!')
      console.log('Transaction ID:', result.txnId)
      console.log('Message:', result.message)
      return true
    } else {
      console.log('❌ Mobile ABHA initiation failed')
      console.log('Error:', result.error)
      console.log('Details:', result.details)
      return false
    }
    
  } catch (error) {
    console.log('❌ Mobile ABHA initiation failed with exception')
    console.log('Error:', error.message)
    return false
  }
}

async function runTests() {
  console.log('🧪 ABDM Session and API Testing\n')
  console.log('================================\n')
  
  const sessionResult = await testSessionGeneration()
  
  if (sessionResult) {
    console.log('\n' + '='.repeat(50))
    await testMobileInitiation()
  } else {
    console.log('\n⚠️  Skipping API tests due to session token failure')
  }
  
  console.log('\n' + '='.repeat(50))
  console.log('📋 Test Summary:')
  console.log('================')
  console.log(`Session Token: ${sessionResult ? '✅ PASS' : '❌ FAIL'}`)
  
  if (sessionResult) {
    console.log('\n🎉 ABDM session token generation is working!')
    console.log('You can now test the full API endpoints.')
  } else {
    console.log('\n🔧 Session token generation needs to be fixed before testing APIs.')
    console.log('Please check the ABDM sandbox connectivity and credentials.')
  }
}

if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test execution failed:', error)
    process.exit(1)
  })
}

module.exports = { testSessionGeneration, testMobileInitiation }
