{"IsEncrypted": false, "Values": {"APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=5551ef8e-d222-43b6-ae4b-7f9ce552e18a;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=3a210e09-1e56-4ece-a0e8-53776ca5bde2", "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=ermdevstoragedata;AccountKey=****************************************************+hQ7dJp3B2ToUMVg0+VnRI52+ASt9xJc/Q==;EndpointSuffix=core.windows.net", "CLIENT_ID": "f22ad9c9-3fe2-4921-b825-ed8b887f3ab7", "CLIENT_SECRET": "****************************************", "COSMOS_DB_CONNECTIONSTRING": "AccountEndpoint=https://emr-dev-cosmosdb.documents.azure.com:443/;AccountKey=****************************************************************************************;", "COSMOS_DB_DATABASE": "ArcaAudioLayer", "DOCKER_CUSTOM_IMAGE_NAME": "ermdevcontainer.azurecr.io/emr-v01/emr-ms:main.171", "DOCKER_REGISTRY_SERVER_PASSWORD": "****************************************************", "DOCKER_REGISTRY_SERVER_URL": "https://ermdevcontainer.azurecr.io", "DOCKER_REGISTRY_SERVER_USERNAME": "ermdevcontainer", "environment": "local_", "FUNCTIONS_EXTENSION_VERSION": "~4", "FUNCTIONS_REQUEST_BODY_SIZE_LIMIT": "*********", "OPENAI_ENDPOINT": "https://erm-dev-openai.openai.azure.com", "OPENAI_KEY": "********************************", "OPENAI_MODEL": "emrsummary4o", "signin_policy": "B2C_1_emrapp", "SummaryInfo": "presentingcomplaint,historyofpresenting,pastmedicalhistory,pastsurgicalhistory,familyhistory,addictionhistory,diethistory,physicalactivityhistory,stresshistory,sleephistory,currentmedicationhistory", "TENANT_ID": "cecfdadd-c501-4007-8619-84df7c41930b", "TENANT_NAME": "erm20240520", "WEBSITE_ENABLE_SYNC_UPDATE_SITE": "true", "WEBSITE_HTTPLOGGING_RETENTION_DAYS": "7", "WEBSITES_ENABLE_APP_SERVICE_STORAGE": "false", "FUNCTIONS_WORKER_RUNTIME": "node", "REDISCACHEHOSTNAME": "emrdevcache.redis.cache.windows.net", "REDISCACHEKEY": "uCKqYP4eyambfCQG0HoxCnOP2eJUJ2pLRAzCaJQMsuM=", "EXPIRED_TIME": "120000", "AZURE_STORAGE_ACCOUNT_NAME": "ermdevstoragedata", "AZURE_STORAGE_ACCOUNT_KEY": "****************************************************+hQ7dJp3B2ToUMVg0+VnRI52+ASt9xJc/Q==", "EMAIL_USER": "<EMAIL>", "EMAIL_PASSWORD": "vswv sjst uype tgac", "JWT_SECRET": "default-secret-key", "BASE_ADMIN_URL": "https://arca-ai-admin.vercel.app", "BASE_URL": "https://arca-emr.vercel.app", "ABDM_BASE_URL": "https://dev.abdm.gov.in/gateway", "ABDM_AUTH_URL": "https://dev.abdm.gov.in/gateway", "ABDM_SESSION_URL": "https://dev.abdm.gov.in/gateway/v0.5/sessions", "ABDM_CLIENT_ID": "SBXID_009193", "ABDM_CLIENT_SECRET": "4df7c42b-46c0-4b1b-95b6-35c6a5317708"}, "Host": {"CORS": "*"}}