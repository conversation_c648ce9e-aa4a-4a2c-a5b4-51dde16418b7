# ABDM Integration Documentation

## Overview

This document describes the ABDM (Ayushman Bharat Digital Mission) integration for ABHA (Ayushman Bharat Health Account) number generation, verification, and management in the EMR system.

## Features Implemented

### 1. ABHA Number Generation
- **By Aadhaar**: Generate ABHA number using Aadhaar number with optional mobile number
- **By Mobile**: Generate ABHA number using mobile number only
- **OTP Verification**: Verify OTP sent during the generation process
- **Complete Creation**: Finalize ABHA number creation with user profile data

### 2. ABHA Details Fetching
- **By ABHA Number**: Retrieve ABHA details using the ABHA number
- **By Mobile Number**: Retrieve ABHA details using the registered mobile number

### 3. ABHA Number Verification
- **Verification**: Verify if an ABHA number is valid and active

### 4. Additional Features
- **Resend OTP**: Resend OTP for any ABDM operation
- **Session Management**: Automatic session token management with caching
- **Error Handling**: Comprehensive error handling and logging

## API Endpoints

### Base URL
```
https://your-function-app.azurewebsites.net/api
```

### 1. Initiate AB<PERSON> Creation by <PERSON><PERSON><PERSON><PERSON>
**POST** `/abdm/initiate/aadhaar`

**Request Body:**
```json
{
  "aadhaar": "123456789012",
  "mobile": "**********"
}
```

**Response:**
```json
{
  "success": true,
  "txnId": "uuid-transaction-id",
  "message": "OTP sent successfully"
}
```

### 2. Initiate ABHA Creation by Mobile
**POST** `/abdm/initiate/mobile`

**Request Body:**
```json
{
  "mobile": "**********"
}
```

**Response:**
```json
{
  "success": true,
  "txnId": "uuid-transaction-id",
  "message": "OTP sent successfully"
}
```

### 3. Verify OTP
**POST** `/abdm/verify-otp`

**Request Body:**
```json
{
  "txnId": "uuid-transaction-id",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "txnId": "uuid-transaction-id",
  "message": "OTP verified successfully"
}
```

### 4. Complete ABHA Creation
**POST** `/abdm/complete-creation`

**Request Body:**
```json
{
  "txnId": "uuid-transaction-id",
  "profileData": {
    "firstName": "John",
    "lastName": "Doe",
    "gender": "M",
    "yearOfBirth": "1990",
    "email": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "success": true,
  "abhaNumber": "12-**************",
  "abhaAddress": "john.doe@abha",
  "message": "ABHA number created successfully"
}
```

### 5. Get ABHA Details by Number
**POST** `/abdm/details/by-number`

**Request Body:**
```json
{
  "abhaNumber": "12-**************"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "healthIdNumber": "12-**************",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": "1990",
    "mobile": "**********",
    "email": "<EMAIL>"
  },
  "message": "ABHA details retrieved successfully"
}
```

### 6. Get ABHA Details by Mobile
**POST** `/abdm/details/by-mobile`

**Request Body:**
```json
{
  "mobile": "**********"
}
```

### 7. Verify ABHA Number
**POST** `/abdm/verify-number`

**Request Body:**
```json
{
  "abhaNumber": "12-**************"
}
```

**Response:**
```json
{
  "success": true,
  "isValid": true,
  "status": "ACTIVE",
  "message": "ABHA number verification completed"
}
```

### 8. Resend OTP
**POST** `/abdm/resend-otp`

**Request Body:**
```json
{
  "txnId": "uuid-transaction-id"
}
```

## Environment Configuration

Add the following environment variables to your `local.settings.json` or Azure Function App configuration:

```json
{
  "ABDM_BASE_URL": "https://abhasbx.abdm.gov.in/abha/api/v3",
  "ABDM_CLIENT_ID": "SBXID_009193",
  "ABDM_CLIENT_SECRET": "4df7c42b-46c0-4b1b-95b6-35c6a5317708"
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": {
    "errors": ["Validation error 1", "Validation error 2"]
  }
}
```

## Validation Rules

### Aadhaar Number
- Must be exactly 12 digits
- Required for Aadhaar-based operations

### Mobile Number
- Must be exactly 10 digits
- Required for mobile-based operations

### OTP
- Must be exactly 6 digits
- Required for OTP verification

### ABHA Number
- Format: `XX-XXXX-XXXX-XXXX` (with hyphens) or `XXXXXXXXXXXXXX` (14 digits without hyphens)
- Required for ABHA-related operations

### Profile Data
- `firstName`: Required string
- `lastName`: Required string
- `gender`: Required, must be 'M', 'F', or 'O'
- `yearOfBirth`: Required, must be between 1900 and current year

## Testing

### Health Check
**GET** `/abdm?operation=health`

Returns service health status.

### Combined Endpoint
**POST** `/abdm?operation={operation_name}`

All operations can also be accessed through the combined endpoint using the operation parameter.

## Security Considerations

1. **Authentication**: All endpoints use Azure Function authentication
2. **Session Management**: ABDM session tokens are cached and automatically refreshed
3. **Data Validation**: All inputs are validated using dedicated models
4. **Error Logging**: Comprehensive logging for debugging and monitoring

## Integration with Patient Records

The ABHA number can be stored in the patient model's `proof.abhaNumber` field for future reference and integration with ABDM services.

## Support

For issues or questions regarding ABDM integration, please refer to:
- ABDM Sandbox Documentation: https://sandbox.abdm.gov.in/
- ABDM Developer Portal: https://developers.abdm.gov.in/
