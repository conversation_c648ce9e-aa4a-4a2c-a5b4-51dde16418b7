{"info": {"_postman_id": "611e303c-d6a9-44fc-9c97-dfe1ef36f591", "name": "Hid-Apis", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "Combine V1 & V2 Api", "item": [{"name": "Authentication", "item": [{"name": "/auth/init", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);\r", "\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"Unable to process the current request due to incorrect data entered.\");\r", "});\r", "\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(500);\r", "});\r", "\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"An unexpected error has occured. Please try again in sometime.\");\r", "});\r", "\r", ""]}}], "id": "ff9aab35-b922-44f9-9fd7-c5e54d34cd28", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"authMethod\": \"AADHAAR_OTP\",\r\n    \"healthid\": \"91-**************\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/init"}, "response": []}, {"name": "auth/confirmWithAadhaarBio", "id": "d8a8162c-9e67-4ae9-a7eb-c96302650a29", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"authType\": \"FINGERSCAN\",\r\n    \"bioType\": \"FMR\",\r\n    \"pid\": \"PD94bWwgdmVyc2lvbj0iMS4wIj8+DQo8UGlkRGF0YT4NCiAgPFJlc3AgZXJyQ29kZT0iMCIgZXJySW5mbz0iU3VjY2VzcyIgZkNvdW50PSIxIiBmVHlwZT0iMCIgbm1Qb2ludHM9IjM5IiBxU2NvcmU9IjY4IiAvPg0KICA8RGV2aWNlSW5mbyBkcElkPSJNQU5UUkEuTVNJUEwiIHJkc0lkPSJNQU5UUkEuV0lOLjAwMSIgcmRzVmVyPSIxLjAuMyIgbWk9Ik1GUzEwMCIgbWM9Ik1JSUVHRENDQXdDZ0F3SUJBZ0lFQWdiTWdEQU5CZ2txaGtpRzl3MEJBUXNGQURDQjZqRXFNQ2dHQTFVRUF4TWhSRk1nVFdGdWRISmhJRk52Wm5SbFkyZ2dTVzVrYVdFZ1VIWjBJRXgwWkNBM01VTZXMG1SZz08L0RhdGE+DQo8L1BpZERhdGE+\",\r\n    \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/confirmWithAadhaarBio"}, "response": []}, {"name": "/auth/authWithMobile", "id": "46004d11-f960-4224-bc38-9b4a92d494bf", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"healthid\": \"91-**************\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/authWithMobile"}, "response": []}, {"name": "/auth/authWithMobileToken", "id": "d7e4aa1a-d8b3-4014-a422-00b668750f44", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"gender\": \"Male\", \"healthId\": \"deep<PERSON>ndhm\", \"name\": \"kishan kumar singh\", \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c\", \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\", \"yearOfBirth\": 1994}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/authWithMobileToken"}, "response": []}, {"name": "/auth/generate/access-token", "id": "c5a54c02-ea1f-4b18-b852-defd6c9a2562", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"refreshToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/generate/access-token"}, "response": []}, {"name": "/auth/mobile-email/preVerification", "id": "ed88a9de-91bd-4488-978e-cf5fe1fec371", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": \"string\",\r\n    \"txnId\": \"string\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/mobile-email/preVerification"}, "response": []}, {"name": "/auth/mobile-email/userToken", "id": "43a00bac-e6e3-4b34-8904-c84e2d7172a4", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \"phrAddress\": \"user@abdm\", \"transactionId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/mobile-email/userToken"}, "response": []}, {"name": "/auth/confirmWithAadhaarOtp Copy", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value2 = jsonData.token\r", "console.log(value2)\r", "pm.environment.set(\"X-Token\",value2);"]}}], "id": "e94a38fa-b7ec-435a-a50f-ec4fc6efdb36", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": \"606090\",\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/confirmWithAadhaarOtp"}, "response": []}, {"name": "/auth/resendAuthOTP", "id": "e0f617cb-ad54-454d-a98c-3c4facbde5e0", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \"authMethod\": \"AADHAAR_OTP\", \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/resendAuthOTP"}, "response": []}, {"name": "/auth/confirmWithMobileOTP", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value2 = jsonData.token\r", "console.log(value2)\r", "pm.environment.set(\"X-Token\",value2);"]}}], "id": "5afe8436-b172-43f2-8b37-7ce11a268303", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": \"string\",\r\n    \"txnId\": \"{{txnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/confirmWithMobileOTP"}, "response": []}, {"name": "/auth/authPassword", "id": "58ed90bc-712c-416e-8b96-794d358f3d70", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U7ICjE4Kyyc9Zf6yQBIE6ApxoKjlgl_O4kCJ9hqpBvttvqxgz9L632dFMmBSvUrfzyS17rSuhC-onbz3U81Ca1Ea-7yD2e69oSnRtZCHJLa1ymA8PbzP8mBe2niL2TSdTEhF0_Bsc1p1Q301tIrlKeFCm8ntX-oTngCOUTNkvtp9cs6j7PLAGL7qKkKzJWwz5VD9k55cauth8XJksWNzkUYT6dQuwdwpCyU_ezMlCpYyLY8jcsWO80-KR6U85NoZREKz1k64_kQsq1b3SrBWgJdvpuMlfq-UsBYRQOF9AKvs-4bfN2cpE7Ci-tjyTPNlHLibgKnJs2LkrdBc37Tewg"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"healthId\": \"91-**************\",\r\n    \"password\": \"India@143\"\r\n}"}, "url": "{{BaseURI}}{{BasePath}}/v1/auth/authPassword"}, "response": []}, {"name": "/auth/confirmWithDemographics", "id": "38daa61b-19fa-4b54-98c7-abf38ed9e198", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"gender\": \"<PERSON>\",\r\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n    \"txnId\": \"{{TxnId}}\",\r\n    \"yearOfBirth\": 1998\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/auth/confirmWithDemographics"}, "response": []}, {"name": "/auth/cert", "id": "f53e1721-a934-43df-8e70-64376603b2f3", "request": {"method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}], "url": "{{BaseURI}}/{{BasePath}}/v2/auth/cert"}, "response": []}, {"name": "/auth/reactivate/init", "id": "fb72ba51-00c7-4dcd-9227-a94db84121a8", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"authMethod\": \"AADHAAR_OTP\", \"healthid\": \"43-**************\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/auth/reactivate/init"}, "response": []}, {"name": "https://healthidsbx.abdm.gov.in/api/v2/auth/reactivate", "id": "9995f977-851e-4751-bb27-4dc54b6a4e63", "request": {"method": "POST", "header": [{"key": "Accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-GB,en;q=0.5"}, {"key": "Authorization", "value": "Captcha_03AEkXODCShAg1WtFGsok7xH38L7iSAhBl97W1WN07vtsMki5FKAfYaKkg1jUlI3lrC0A3OFeGljjqGBA8WZCrPMI_I2AMmp5mKs4g4vZ6j8Ivmkp3jkrkRze46qRCeNSiR3Bg9_eKEcwvBoOrvNgEHAM31RcDuYIWxtIZCPv7jUPW4hXyn5be6UgxXw72cxhasI4ci0gpG-YVEqAPC5sL65HAEZdJBbyTbjGl6BdV_9ONRPYwBbKQ5nyy1uGP8mcjwqcpk0AFjbEsMYR0yGfYzvP4bXGcnkx8F3YDXyBVbSxDczsV2hZBrF_B_9A8vd3IDbDLJTPHdQFUWFEhmLp15dEgeLVysF4xIzqKSy0yoXLt9fEPtq9g-nasejjfuZaEr-QDHdp3Ipb9kU837pvNMGEGH0ff-Pq-_KnzhrtLR8hkOzI1qCCKc0ozAIabE7zBFAWnoYNbdsgalwxEtjDXY5MJr9MM4DY79yple2ph_xVvOAY7YShBMmHO7wg3FEOxOz6bCt3b_IWGfVgjxWoRvL2w5EIuvq3tySo4hhBDOwx4r_iLA_v7MDyR43gbPZmBg8owNlLFIw6lQJnIWuYXOrNyBPs5ROtkzcJr_DAywHURpkAgVdLhbXklBx23oyLuDny3vQ2TO59u-dAyY7M_3DjnYnfDz3_ypzXIt7b9SjpBTaxbDXckM0F9sRvhbfW2fOeXIlbcygVvDte0Bo8ehd0gFET6bFw6uvD684AcKZlQzk_c8cvuHeIF3HgyQRzsWoSVDi4kJFOpBItVnOzyjzfKzlQmy7cLVlW87Sw9D0L2v0_qznpOD2oiDf4gyox2kT2YM7QJT2NDyEdADLN4w5rkCbTCv9zmj5j31d2LsyhPpbmm0fUaSVPdZZpLulEOL3XtAqTpJrcV2xSjsEWlX7ne9XNgjcJ9smn4BmlUO9XMyXl1ZP8dpNnN2evHMX2lGw9D6lZvzNiH-IGicOwqsd4Z_eV5EGieUPAx2_pay6DFC5jCoPeCrfgOe_oS7y8hq-GTlEUV8QC9dDZpe-rOC7Mm2HPGHdABYC3JTyZy__VhYeJaHePigp2OKQxFtKra92drR5TZ2VSDXtGc--jRDjX3KlpUqw6iolUqNY5Qssywrg2lI4fLni821B1zj6ZSJ2KWWTFxwjkzxml6-9cK3JYWlZzJcQ0T1JoRSFrO9fOYnhU8QO9ztsVrCkwt3nD0NpGGgTGJOdp9aJsMumbdWLOPCCyG_qGS_VFlshgi_brfJk5-HNTuv224iZzODiUw1pgUc5oADGfASL3AGLbYj6n4rokK45Px-MafKBUyIIn9y0jMCzH4QaVGmtCNEkQ0Y3lzjYVOFb5_bmMuyBaNNbXSFGA0lnnVC4cstRv3z5_O3YOHZh-AZfx4nIcSsw1wBJnrKgPWy7N9IVEe1rASrjGpdQdwbCLLvJ7w8nSl5cinHMbGajxLjV0Aec1jyaUmbvSur1SJJs1UgosSdeemjBQMd-83T9QxDKphMYrH-1myYBdPIR2LTDV-qMssLAC0seSklJ_QFaIDnT3yEiDR8q9OLm27hTzOlC5iJQQ3gw9EG7gnesPreDDrbuLjwsAA58PEyU22zZJ0YeMG3ZWlZy232SzORR4zQXm9uUj4W1n--6tKIpwwkpxu0AaFtbiCRtHHPCU1Srej"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8"}, {"key": "<PERSON><PERSON>", "value": "TS01c078b5=01115a1c90d8d9724eeb1ff12494d855bbc3c571c4321d3fa958d5bd9e2ea5b770dcef9d5e564c66b325768fe0c2dece8a67affdd3"}, {"key": "Origin", "value": "https://healthidsbx.abdm.gov.in"}, {"key": "<PERSON><PERSON><PERSON>", "value": "https://healthidsbx.abdm.gov.in/login/reactivate?healthId=91-**************&authMethods=AADHAAR_OTP,AADHAAR_BIO,MOBILE_OTP,PASSWORD,DEMOGRAPHICS"}, {"key": "Sec-Fetch-Dest", "value": "empty"}, {"key": "Sec-Fetch-Mode", "value": "cors"}, {"key": "Sec-Fetch-Site", "value": "same-origin"}, {"key": "Sec-GPC", "value": "1"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"key": "X-Token", "value": "Bearer eyJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************.r9VkGFWKiNgXvrWZHicS6MgDW3zLXRtKqlhxZwdG0OOksb14GV65YafrKUtBilMiJD3_4RLL8_wZMh_pRPR5chMMldZ0da922hRP3phGtYz-EZF0FgU4U_brJhZ_Z9Qh7VfROuRUiawlugky9lk0XqvBeo7uaixNI7ESxSYdmB0ZSyXGGnZOZE255Lg_TVsfMiRu9CkxtdWjuxtX1tAk1G3GxMooaTDkgAJqxYvYJoBYZPTZb3rqTmCgTwyM5hqmI5mw0mMGk7GqoqCffbWnHbW-pJrbJmG6iJfFaAlSWUvrwIgRD-U7tetSztTFgQnqNiU2RcOrkf2U3vRcQx6LyB1KeuCqMvU-LpnnyzOsW3hillM6FGGZD6r1icZNP0X0VV7Gb97QMxqSr7gFJGzII2dvw1PK3mISctaRs-MvIvHxeIn0JW_n0EK9guujXSkgPUXhSrgbsSOzgiaDuvwInoyJ4X2VqPQUnCnJ-qX5BzsRlU-k0T5bzYfT6zAdKFmDY8WKn6OBHJSOrsovFUMPdN1TbEAwcB0RGSlJhhuZjXjhz_3l3SQ2WEoYI0TlEQJHoLd8PGjbRJvDcD8eM-37fMsDlq2RZE8kkJWiKa6DC7JMY2K9lE2h8pBKfqnsq3mOhleYVPTBZubBCnabJ5noHYiEOctfFBvbbHBY0nS_sE4"}], "body": {"mode": "raw", "raw": "{\"txnId\":\"cf77219d-35b2-47da-b84e-f5dee6ce61ee\",\"otp\":\"V6u0GB6yWCytfhUM+sfXPz+bl0TCRtmztHL2iBYVLLQVipEbDyk3fK21UEcr0HqM/Zu0RQORjobNyIOh+c9Jzg9WHmkeTwjdPJSIGyNgrEj3VXxzYipSuaZ4f0Llc5F2ZaREEvskcfIExD5y17TxC31t9WTg+aqXatGjYgraYxsWtZ5fFhZux/0j3Y2DM2bqz2/xv/h/MODTkroXCHuvCNBdpeaGRxmgHRfRfXsxrWnONINPUmtKg6WAE1FDu2ve1MjgdXCjOzhPw3eheLxzYPGkcG1q/PGRMy+r9JB8Bx5PmpXzNmr2/mbb+Cj7Bq805lk/e/jCjoHX0Gb8UydH5IvE2msBgGd/vsM8W4a4kQuMYkBrnol+XMI+D3bg30G+XLkPRIK3VZYAiZah3Qix9loDd+fM2FFlC/tYKyFELHNYDf9T0CDHRqVxK2LPxslkKsJX3CrSS8NSiiGnp5k+t0kswsTmEQq+fMZcP0DK+AyXRfqq4xRQYLqdLEw7HE5ey9U0uFAqL9Fsw5Uy11PXHvX/nLYe3Z0//8g8/Xfc475rXq5VyON+/YujBuyAh5BegC20gN5dAvGyiO//Ff5lb3HsGzZJ38sNAiV9AH58PIELbZ8C728SKC/BsYEn7mV7depxhFzKQPQNGlUfxGZUb90BOatNflsIagTOruJrRb0=\",\"authMethod\":\"MOBILE_OTP\"}"}, "url": "https://healthidsbx.abdm.gov.in/api/v2/auth/reactivate"}, "response": []}], "id": "2e177371-9cbd-41dc-920d-305849826a53"}, {"name": "Forgot Healthid/Number", "item": [{"name": "/forgot/healthId/mobile/generateOtp", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "a654f6b1-1e13-4970-8877-20609cbfa21d", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"mobile\": **********\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/forgot/healthId/mobile/generateOtp"}, "response": []}, {"name": "/forgot/healthId/mobile", "id": "e8de091b-0715-4dd8-8ed6-b8181bb6a25a", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"dayOfBirth\": 9,\r\n    \"firstName\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n    \"flow\": \"RETRIEVE_HID\",\r\n    \"gender\": \"M\",\r\n    \"lastName\": \"<PERSON>want\",\r\n    \"middleName\": \"\",\r\n    \"monthOfBirth\": \"11\",\r\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON> Gunwant\",\r\n    \"otp\": 621313,\r\n    \"status\": \"Active\",\r\n    \"txnId\": \"{{TxnId}}\",\r\n    \"yearOfBirth\": 1998\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/forgot/healthId/mobile"}, "response": []}, {"name": "/forgot/healthId/aadhaar/generateOtp", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "ab159bf4-17e9-4af5-9a84-28e644667d9a", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadhaar\": 31541756999\r\n}"}, "url": "{{BaseURI}}/{{BaseURI}}/v1/forgot/healthId/aadhaar/generateOtp"}, "response": []}, {"name": "/forgot/healthId/aadhaar", "id": "93f5e58b-817a-47cc-ab09-d02d474a7c73", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": 816406,\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BaseURI}}/v1/forgot/healthId/aadhaar"}, "response": []}], "id": "5e3f70b8-7434-4a48-85a3-380b8be2fd8d"}, {"name": "Integrated Api", "item": [{"name": "/hid/benefit/update/phr-address", "id": "377bb837-58cf-4a5a-ae72-edc835abd842", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text", "disabled": true}, {"key": "X-Token", "value": "Bearer {{X-Token}}", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"healthIdNumber\": \"91-**************\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/update/phr-address"}, "response": []}, {"name": "/hid/benefit/aadhaar/generateOtp", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "aefeb874-9070-4b37-b492-8b3fa3b456a4", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadhaar\": ************\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/aadhaar/generateOtp"}, "response": []}, {"name": "/hid/benefit/aadhaar/verifyAadharOtp", "id": "f301d976-c8a2-4c83-94a8-1c6b353e2215", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"autoGeneratedBenefitId\": false,\r\n    \"benefitId\": \"\",\r\n    \"benefitName\": \"healthid api\",\r\n    \"consentHealthId\": true,\r\n    \"mobileNumber\": \"**********\",\r\n    \"otp\": 466481,\r\n    \"txnId\": \"{{TxnId}}\",\r\n    \"validity\": \"\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/aadhaar/verifyAadharOtp"}, "response": []}, {"name": "/hid/benefit/aadhaar/verifyBio", "id": "a80c9422-c381-494c-bec6-6194db859cf3", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadhaar\": 31541756999,\r\n    \"autoGeneratedBenefitId\": true,\r\n    \"benefitId\": \"axsp1233525903\",\r\n    \"benefitName\": \"Govt of AP\",\r\n    \"bioType\": \"FMR\",\r\n    \"consentHealthId\": true,\r\n    \"mobileNumber\": **********,\r\n    \"pid\": \"PD94bWwgdmVyc2lvbj0iMS4wIj8+DQo8UGlkRGF0YT4NCiAgPFJlc3AgZXJyQ29kZT0iMCIgZXJySW5mbz0iU3VjY2VzcyIgZkNvdW50PSIxIiBmVHlwZT0iMCIgbm1Qb2ludHM9IjM5IiBxU2NvcmU9IjY4IiAvPg0KICA8RGV2aWNlSW5mbyBkcElkPSJNQU5UUkEuTVNJUEwiIHJkc0lkPSJNQU5UUkEuV0lOLjAwMSIgcmRzVmVyPSIxLjAuMyIgbWk9Ik1GUzEwMCIgbWM9Ik1JSUVHRENDQXdDZ0F3SUJBZ0lFQWdiTWdEQU5CZ2txaGtpRzl3MEJBUXNGQURDQjZqRXFNQ2dHQTFVRUF4TWhSRk1nVFdGdWRISmhJRk52Wm5SbFkyZ2dTVzVrYVdFZ1VIWjBJRXgwWkNBM01VTZXMG1SZz08L0RhdGE+DQo8L1BpZERhdGE+\",\r\n    \"validity\": \"2007-12-03\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/aadhaar/verifyBio"}, "response": []}, {"name": "/v1/hid/benefit/createHealthId/demo/auth", "id": "9cb37154-e81f-4244-819f-807ae7759146", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadharNumber\": ************,\r\n    \"autoGeneratedBenefitId\": true,\r\n    \"benefitId\": \"\",\r\n    \"benefitName\": \"healthid api\",\r\n    \"consentHealthId\": true,\r\n    \"dateOfBirth\": \"09/11/2021\",\r\n    \"gender\": \"M\",\r\n    \"mobileNumber\": **********,\r\n    \"name\": \"Shobit Gunwant\",\r\n    \"validity\": \"\"\r\n}"}, "url": "https://healthidsbx.abdm.gov.in/api/v1/hid/benefit/createHealthId/demo/auth"}, "response": []}, {"name": "/hid/benefit/notify/benefit", "id": "ed8ea63e-37ee-4989-ad2c-1385c5918792", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadharNumberOrUidToken\": \"01111481Nd0E15s9fdWmx6avX/q5Gua/Za56BbGXvFr5wMaHvvFwRQY7MBIaOsByshXDRVz0\",\r\n    \"autoGeneratedBenefitId\": false,\r\n    \"benefitId\": \"\",\r\n    \"benefitName\": \"healthid api\",\r\n    \"consentHealthId\": true,\r\n    \"dateOfBirth\": \"09-11-1998\",\r\n    \"gender\": \"M\",\r\n    \"mobileNumber\": **********,\r\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n    \"stateCode\": 27,\r\n    \"validity\": \"\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/notify/benefit"}, "response": []}, {"name": "/hid/benefit/search/aadhaar", "id": "0c920cc9-dc17-43ec-a2c0-7aecfbab7710", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadhaar\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/search/aadhaar"}, "response": []}, {"name": "/hid/benefit/search/healthIdNumber", "id": "d71a8ff4-59bc-418a-ac7c-736fe317c96c", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"benefitId\": \"7347204510229311\",\r\n    \"healthId\": \"91-**************\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/search/healthIdNumber"}, "response": []}, {"name": "/hid/benefit/update/mobile", "id": "d4c37cf1-9f2e-46e3-900b-8f724a172089", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"healthIdNumber\": \"91-**************\",\r\n    \"mobile\": **********\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/update/mobile"}, "response": []}, {"name": "/hid/benefit/update/profile", "id": "ed457095-7bba-4f07-9c10-a7b1acfe0118", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"address\": \"b-14 someshwar nagar\",\r\n    \"dayOfBirth\": 9,\r\n    \"districtCode\": 603,\r\n    \"email\": \"<EMAIL>\",\r\n    \"firstName\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n    \"healthId\": \"\",\r\n    \"healthIdNumber\": \"91-**************\",\r\n    \"lastName\": \"Gunwant\",\r\n    \"middleName\": \"\",\r\n    \"monthOfBirth\": \"11\",\r\n    \"password\": \"India@143\",\r\n    \"pincode\": \"\",\r\n    \"profilePhoto\": \"\",\r\n    \"stateCode\": 35,\r\n    \"subdistrictCode\": \"\",\r\n    \"townCode\": \"\",\r\n    \"villageCode\": \"\",\r\n    \"wardCode\": \"\",\r\n    \"yearOfBirth\": 1994\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/update/profile"}, "response": []}, {"name": "/hid/benefit/update/status", "id": "4cb1fabc-b055-490e-a9e0-06cdbb81b48a", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"healthIdNumber\": \"43-**************\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/update/status"}, "response": []}, {"name": "/hid/benefit/mobile/generateOtp", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "c44c334f-5456-4eab-a448-49a0d0476da5", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"mobile\": **********\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/mobile/generateOtp"}, "response": []}, {"name": "/hid/benefit/mobile/createHealthId", "id": "02309e71-0e53-4ed5-9b71-8520e398836f", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"autoGeneratedBenefitId\": false,\r\n    \"benefitDocType\": \"Driving License\",\r\n    \"benefitId\": \"\",\r\n    \"benefitName\": \"healthid api\",\r\n    \"consentHealthId\": true,\r\n    \"dateOfBirth\": \"09/11/1998\",\r\n    \"docNumber\": \"\",\r\n    \"fileType\": \"\",\r\n    \"gender\": \"M\",\r\n    \"name\": \"k<PERSON><PERSON> kumar singh\",\r\n    \"otp\": 333752,\r\n    \"txnId\": \"{{TxnId}}\",\r\n    \"uploadedDoc\": \"\",\r\n    \"validity\": \"\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/hid/benefit/mobile/createHealthId"}, "response": []}], "id": "8189aca5-918f-479a-9bea-64d4f72781ea", "auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "event": [{"listen": "prerequest", "script": {"id": "6ebf1881-7d4d-43e0-91dd-3204167aecaf", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "2568723a-074e-4a3b-ab24-198e06a7a38d", "type": "text/javascript", "exec": [""]}}]}, {"name": "Profile", "item": [{"name": "/account/logout", "id": "1fc642d3-e1b4-4732-aed1-65e04cb454c4", "request": {"method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bear<PERSON>"}], "url": "{{BaseURI}}/{{BasePath}}/v1/account/logout"}, "response": []}, {"name": "/account/profile", "id": "5ba3c4fb-723b-45da-bacd-b9f4d988575c", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer {{X-Token}}"}, {"key": "Authorization", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Fg_9eUihXOXwZ5utgCqBz8JK3r86_hW7js0UD54qkNxdrkhYGTgQzzSk1ZzvYyOPRlk2wWWY2nQ4DVRb8dl687qRNHa6at2np4Qdr4CwQbNQhYv9Ef5e4JmqBLURhe_9enIYmmBP26PIRWmCfBTaGrspTvmJ5lw_L0QZjNjJ_0YxcVWLmqX0eZImNFVZ_2QW4Nm9WGh2XH7Cfzv3xwB2BzM71hKooo1rmodoFSnoNnoX2w0zZP3iwbfz7OnoOGTfEhaJT6Ryy_c5Q7PSAR7Rp5_PNdfYWIuXVTS594zrwB1WVOP5vSb4J0vyjI_7OQz-yCl4BQN0JmfKF_l2ytwcBw", "type": "text", "disabled": true}], "url": "{{BaseURI}}/{{BasePath}}/v1/account/profile"}, "response": []}, {"name": "/account/profile", "id": "f9085adf-0386-4e89-91a7-8ada8ef8a866", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer {{X-Token}}"}, {"key": "Content-Type", "value": "application/json"}, {"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"address\": \"\",\r\n    \"dayOfBirth\": 9,\r\n    \"districtCode\": \"603\",\r\n    \"email\": \"\",\r\n    \"firstName\": \"Shobhit\",\r\n    \"healthId\": \"\",\r\n    \"lastName\": \"Gunwant\",\r\n    \"middleName\": \"\",\r\n    \"monthOfBirth\": \"11\",\r\n    \"password\": \"\",\r\n    \"pincode\": \"\",\r\n    \"profilePhoto\": \"\",\r\n    \"stateCode\": \"35\",\r\n    \"subdistrictCode\": \"\",\r\n    \"townCode\": \"\",\r\n    \"villageCode\": \"\",\r\n    \"wardCode\": \"\",\r\n    \"yearOfBirth\": 1998\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/account/profile"}, "response": []}, {"name": "/account/qrCode", "id": "095d8e50-6855-4868-86fd-4036dc05d44d", "request": {"method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bear<PERSON>"}], "url": "{{BaseURI}}/{{BasePath}}/v1/account/qrCode"}, "response": []}, {"name": "/account/getCard", "id": "4f2a5b38-1283-4cce-a881-d04adb2dc278", "request": {"method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bear<PERSON>"}], "url": "{{BaseURI}}/{{BasePath}}/v1/account/qrCode"}, "response": []}, {"name": "/account/getSvgCard", "id": "1aeaedec-8c84-437d-b78d-616d1ff9d614", "request": {"method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bear<PERSON>"}], "url": "{{BaseURI}}/{{BasePath}}/v1/account/qrCode"}, "response": []}, {"name": "/account/getPngCard", "id": "2984a7fd-a430-4e28-a602-c70e1f4a95f5", "request": {"method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bear<PERSON>"}], "url": "{{BaseURI}}/{{BasePath}}/v1/account/qrCode"}, "response": []}, {"name": "update/phr-address", "id": "007dfd29-05f3-4460-b63a-fdd04e667a85", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer {{X-Token}}"}], "url": "https://healthidsbx.abdm.gov.in/api/v1/account/update/phr-address"}, "response": []}, {"name": "/account/aadhaar/generateOTP", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "38c517c1-9051-44a8-beab-1c3e38d68803", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "<PERSON>er X-Token"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadhaar\": \"pAPckzj4SUhRVUSCZZZXmLDGEpGGbRTxFpnJ7k5086H7bH1ux+WjyrM1cCJBFT3LZsEK8ukQ5Vu0gyDNZQ6+/0oykDwPkcbSqOEbAk6AObJ75mXoun3faU1kB1Qgy5qFMjSup9pUnPHyDcWLB+dfOv7QjfGNEbutR36MRAn6ZjgwuZOsWpVXhaRenXs44xCOF99Kd7r2ZcT+U/wDx8ENBjCSUgpIuyaLVTOyJWFq1bunN7ujQ/COx2YBmHJ06tQITMtHKCVsaRfnSQ6qpQw7KG+cmYbXZ/rauGZ4eJiy3tj7RO/74v6NvwoiO15s/BM7h39bchdN3z++YETizGTrjIZbLPOGenXZOtyJU0wfLzNBg3OGmC6PSLL+JYc5vPedcoaTep6MxWiFiMNR5OZ54ELN6eNZamWZ0/mBZllzIoi1C6occgvv93WXcCta5QNb6B12vB2zUZgnPHdVKALrZV5w/9adu9H6CYYq9nSTMpZvlVvMhEHdThB25dXUuwvxbd3J8G3iNNBq1agEPsUI6vQ/9ytsBAMZs+H7CzgsP/EdZVn+q1kiJCrsl3a6CZWH9yqCIAocxCyCMod+FIWLVEiD2Dl5jm3a9QNjZXZSWAg+GZAWVZ7Ln6TllMtROoWUG2nxW4lcxWMZ+TwX80hT6n/HG9rxqUgp1q5FPxKVERE=\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/account/aadhaar/generateOTP"}, "response": []}, {"name": "/account/aadhaar/verifyOTP", "id": "aaff3d28-6357-4f07-a80d-7315c5aa83e3", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OQ10igfuqFf7w9-wTORr9CAl-laSFNNHP0IY1C3M3bURqWw32Cwix5SivRw1igfyQLGwSve1GPhf1SrTJhACatRLEIuNqShEFgw3-9uQhkJfKymUdzAT7eKQfIDCKXb4aw8PdDY_T8IKDZtLwP1hxBiBDdEzm8MQpA4rVLsx6Gedzq7i7RhicBVGELFAurUuYlhSpcS6L7_zN-lUqOFsjql0T9t7UxyS85PFF5zIQqYz1fmYioOolFEVSaQEJbCaga0VTCDwINhDpxmKBLkTFVRmLG_1S2TNRdHwOwe1UuPBCBJA9huozs-pOCXY-RWgJMG55D8NB3NHoXKeAnGl2Q"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer eyJhbGciOiJSUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************.JFCtXaEbY8mfQSHfzNx61uakFWn-UEr1akGYyvp1IjZG4kNLxI9rVLz_GaRAYIWqRBFIxx2ct4StXeYt6DtMBOkn8N2GhaWW0OZR3ZrqkxZ0vcNyBZ87M1Qzh7P2RiIVenZ-SVrSoPMGToYEYmFSSb41FmyN2dlxQ3_OHKDoAVtDFew0BwdevVZBIw6mNvNDzkpbjlnSiwcgdXugrbW6gWyawp7zoGUYuurNS3eTP8_r8Ab0GzQSrVFXTIsE0t2CiyrWQeejvlk8BLW7-53bmus9jo686MGthUVkOdoQR8rjo9vP0jiwMtCmDlmhi9zzxLgvWADJS0smUUFCksXHPrJos0IJDLvlOdgd7Q1AjRE-HpTg9gXAE2r_UfKE-UWLe3RKbhldSaIJyLY9u00usOUNzg0JA8vWOro27mA8pJ6VaLq_BXB6dMmg3ly0QfXf_adjAsnAX-ZH8r2nHt_KH59b1IIP1zjPDUc45x7qVZPerzGnAs1vfbsb2vrZaTNuP74b2j5V3C-ODAckQqfYVvDbhqp2e3YXE62IF52iEVEM85xdIqMlemhVvs2h4-4nSiLZIR601sOhc72NpkP26EPJBvzBeF-hC_VB713BugZWMfdy3s01SxGAb6dzb8zaRxeeBasAa2GmYhaWxWVvjPanY1KBMHtLufOHsh49Xtg"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": 458175,\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/account/aadhaar/verifyOTP"}, "response": []}, {"name": "https://healthidsbx.abdm.gov.in/api/v1/account/change/passwd/generateAadhaarOTP", "id": "a8f1e276-0e3a-4719-a0bb-68ff4acf056a", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer {{X-Token}}"}], "url": "https://healthidsbx.abdm.gov.in/api/v1/account/change/passwd/generateAadhaarOTP"}, "response": []}, {"name": "https://healthidsbx.abdm.gov.in/api/v1/account/change/passwd/generateMobileOTP", "id": "1c2e0110-e591-458f-9282-1b7f6983dd59", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer {{X-Token}}"}], "url": "https://healthidsbx.abdm.gov.in/api/v1/account/change/passwd/generateMobileOTP"}, "response": []}, {"name": "/account/change/passwd/by<PERSON><PERSON><PERSON><PERSON>", "id": "f8f6ce8c-3100-4075-b711-57ae42416c2c", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer eyJhbGciOiJSUzUxMiJ9.********************************************************************************************************************************************.dpS3_aKRE0A16Rp6F2hU6kKsqqfQPPohpx9NQZmV0t1ONmiwOGYkNsj8g-lB591ii7ULbM5Dac_N7Fk2Eog45gVWwBCPT3Yw-dWVNNPxbhoNP4gHrPt67HFB3Oqc3hck98sCNy0HPgetKzqzwRELpECyjCSaK8f3rsJlweuk7xVnaA2Iku13AWIK_Lj6zemM6tdh5n_kAv03yDKuMZojHs-YX6mGCE3MZu2SB7yvpJu2eNpc2wkGU8p1X4TT3T_Gq114DhDe23CtpT3E1c9Z2MjNYll8RlnSQeYTLjjNN1E5y8Pv7nEgmq0e046vhNapSPDp1_tY1v1HwlOkx61DjR_UP2ChU41eQ5v4z-4fuYhRHfiROBhQo2YRmAsqLzLuZWkO2dxoJQiDvtkpm3JHk1-dzr91Rpo3HIIvIgmKm6C77ztvtBPm4cAf5HoNU4K4zZuFo3gc9zHFlVjutdswcogZehQ2ZL_wFrkA4wCjBI4LrCzfEMfJNbb8M7sMUbmS7h7lgvrjfNBqaRCx0tLt88khMUku8s2O-MhRKrQ29hkIJAbPoO_noxFfYligENnAikv49U8TTqwt60TXpqubHVb4CM4PX_pKKL2duVDIn9kCDT1u3tPYALLIfmObzDLs0f3VHlHrKCPC77xYW0nm9-w3pLIcuwUmfU7d-8zd8tQ"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"newPassword\": \"India@143\",\r\n    \"otp\": 838478,\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/account/change/passwd/byA<PERSON><PERSON>ar"}, "response": []}, {"name": "/account/change/passwd/byMobile", "id": "3dd71355-9739-4fb0-888f-e133c31869c5", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "<PERSON>er X-Token"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"newPassword\": \"India@143\",\r\n    \"otp\": 816405,\r\n    \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/account/change/passwd/byMobile"}, "response": []}, {"name": "/account/change/password", "id": "9340e622-ed2b-42c6-95aa-9a0319ebc9df", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "X-Token", "value": "Bearer {{X-Token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"newPassword\": \"India@259\",\r\n    \"oldPassword\": \"India@143\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/account/change/password"}, "response": []}], "id": "347c22d0-6ee4-4beb-9c05-a199a072786e", "auth": {"type": "bearer", "bearer": {"token": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Pqfv3V-LqatAe3tnUzBgk9JCJhQg4sKOAkY-gXn_XEP1hnolPho8OBgHSucUp58PaiNib8f0RH6Jo_b13pYNnVIuB4-1G8BLgGKlZBjxP3maTRvPBd7r36LZitH6AHEpdaRn8ZUcstKJrP8M8H-my1jawtVJN7ywZrJRU7yXmRDmquMTnH38ap4as5I3dADuoVoC9BSTOaNoqRc57i5el_ouNmr5p92OB7MhPf8TNas8d-tVtZcMhszBVK5sZ66poAN30c2do4I-L5Q4LR36mllWzeRGXDmEcV9M-8IVCjnqL6M4243rtVNTPhkyozbVCyLSnuJlFNktCLQKprcPBQ"}}, "event": [{"listen": "prerequest", "script": {"id": "2f96ebfe-087c-467c-a63b-4dbe7b11c038", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "4ec9305c-1dbc-4e4e-be9d-c785726e90e6", "type": "text/javascript", "exec": [""]}}]}, {"name": "RegistrationWithAadhaar", "item": [{"name": "/aadhaar/generateOtp", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "73e78bfa-5ee1-41f5-80ac-bf445346e3f9", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"aadhaar\": ************\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/generateOtp"}, "response": []}, {"name": "/aadhaar/verifyOTP", "id": "db4cc5c6-9fc7-409b-a930-4af8e4e39204", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\":342969,\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/verifyOTP"}, "response": []}, {"name": "/registration/aadhaar/checkAndGenerateMobileOTP Copy", "id": "43d20b0f-cf73-4546-b2a2-7b12c3139373", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"mobile\": **********, \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/registration/aadhaar/checkAndGenerateMobileOTP"}, "response": []}, {"name": "/aadhaar/resendAadhaarOtp", "id": "47b42ce1-caf1-46e5-a0b8-26d8e5088c42", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/resendAadhaarOtp"}, "response": []}, {"name": "/aadhaar/verifyMobileOTP", "id": "be692291-a327-46f7-b66f-dba845356e44", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": \"777106\",\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/verifyMobileOTP"}, "response": []}, {"name": "/aadhaar/generateMobileOTP", "id": "390c2e44-91a4-4599-a095-f4eaf64abd12", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"mobile\": **********,\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/generateMobileOTP"}, "response": []}, {"name": "/aadhaar/createHealthIdWithPreVerified", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value2 = jsonData.token\r", "console.log(value2)\r", "pm.environment.set(\"X-Token\",value2);"]}}], "id": "26e44d77-82d1-41b2-bbc0-9a09d033239f", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"firstName\": \"Shobhit\",\r\n    \"healthId\": \"shobhit2906\",\r\n    \"lastName\": \"<PERSON>wan<PERSON>\",\r\n    \"middleName\": \"\",\r\n    \"password\": \"India@143\",\r\n    \"profilePhoto\": \"\",\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/createHealthIdWithPreVerified"}, "response": []}, {"name": "/aadhaar/createHealthIdWithAadhaarOtp", "id": "13377c75-41f1-421b-b987-7379f85db51a", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"firstName\": \"<PERSON>o<PERSON>\",\r\n    \"lastName\": \"<PERSON>\",\r\n    \"middleName\": \"<PERSON><PERSON>\",\r\n    \"mobile\": **********,\r\n    \"otp\": 812306,\r\n    \"password\": \"India@143\",\r\n    \"profilePhoto\": \"/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkJCQkJCQoLCwoODw0PDhQSERESFB4WFxYXFh4uHSEdHSEdLikxKCUoMSlJOTMzOUlUR0NHVGZbW2aBeoGoqOIBCQkJCQkJCgsLCg4PDQ8OFBIRERIUHhYXFhcWHi4dIR0dIR0uKTEoJSgxKUk5MzM5SVRHQ0dUZltbZoF6gaio4v/CABEIBLAHgAMBIgACEQEDEQH/xAAbAAACAwEBAQAAAAAAAAAAAAACAwABBAUGB//aAAgBAQAAAADwawLpMspcK7qrlE5F0Vtul2bVywMUNeBHUkW/bmxvYELGuNjh2VDvixxo5ViljKjDRMoahCULjs2JCShjhjh2OGxo0Y2MoXHOLszsKLhw7tD99mpZQxj8xceofmLEKFwXLTIyHwY1Ls+iEotjHY0M0pjRYxtGj4VFKLPohQlFQyy4Qipc0XG9pS+CP/2Q==\",\r\n    \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\",\r\n    \"username\": \"User123\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/createHealthIdWithAadhaarOtp"}, "response": []}, {"name": "/registration/aadhaar/verifyBio", "id": "57dc4b00-05c3-49bf-b641-78b582acce0e", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \"aadhaar\": 31541756999, \"bioType\": \"FMR\", \"pid\": \"PD94bWwgdmVyc2lvbj0iMS4wIj8+DQo8UGlkRGF0YT4NCiAgPFJlc3AgZXJyQ29kZT0iMCIgZXJySW5mbz0iU3VjY2VzcyIgZkNvdW50PSIxIiBmVHlwZT0iMCIgbm1Qb2ludHM9IjM5IiBxU2NvcmU9IjY4IiAvPg0KICA8RGV2aWNlSW5mbyBkcElkPSJNQU5UUkEuTVNJUEwiIHJkc0lkPSJNQU5UUkEuV0lOLjAwMSIgcmRzVmVyPSIxLjAuMyIgbWk9Ik1GUzEwMCIgbWM9Ik1JSUVHRENDQXdDZ0F3SUJBZ0lFQWdiTWdEQU5CZ2txaGtpRzl3MEJBUXNGQURDQjZqRXFNQ2dHQTFVRUF4TWhSRk1nVFdGdWRISmhJRk52Wm5SbFkyZ2dTVzVrYVdFZ1VIWjBJRXgwWkNBM01VTZXMG1SZz08L0RhdGE+DQo8L1BpZERhdGE+\"}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/aadhaar/verifyBio"}, "response": []}], "id": "0ab62f0e-2b99-4b83-936a-81ccfc716493"}, {"name": "RegistrationWithMobile", "item": [{"name": "/mobile/generateOtp", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);\r", "\r", "value3 = jsonData.txnId\r", "console.log(value3)\r", "pm.environment.set(\"TxnId\",value3);"]}}], "id": "afe3e98b-def7-416c-82e9-2e7586957585", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"mobile\": **********\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/mobile/generateOtp"}, "response": []}, {"name": "/mobile/resendOtp", "id": "e6ecfae2-69eb-4f54-9a9b-1688c5decfdc", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/api/v1/registration/mobile/resendOtp"}, "response": []}, {"name": "/mobile/verifyOtp", "id": "220264b1-ec69-4098-85d1-263ac42b9602", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": 812884,\r\n    \"txnId\": \"{{TxnId}}\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/mobile/verifyOtp"}, "response": []}, {"name": "/mobile/createHealthId", "id": "48378e38-2954-4f1c-85df-0ea10c3a4703", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"address\": \"b-14 someshwar nagar\",\r\n    \"dayOfBirth\": 27,\r\n    \"districtCode\": 603,\r\n    \"email\": \"<EMAIL>\",\r\n    \"firstName\": \"kishan\",\r\n    \"gender\": \"M\",\r\n    \"healthId\": \"kishan28\",\r\n    \"lastName\": \"Singh\",\r\n    \"middleName\": \"\",\r\n    \"monthOfBirth\": \"5\",\r\n    \"name\": \"kishan\",\r\n    \"password\": \"India@143\",\r\n    \"pincode\": \"\",\r\n    \"profilePhoto\": \"\",\r\n    \"stateCode\": 35,\r\n    \"subdistrictCode\": \"\",\r\n    \"token\": \"eyJhbGciOiJSUzUxMiJ9.**************************************************************************************************************************************************************************************************.YP1ou9spOfm2tkB7Xdr8PDifyVkFu1r-6DMypJdgtOQO4rnLsRbYzmKIxNw9KNFcFlwOm2TN-rQZaRoGAm0sGU-pQOA4ND0Q32qb2etYSoQtZA6Cz2aYyu3Y0SS5iNIqdJ9rO4PoR3BwQaFZQX0epWuf8Z97V92BztVu3pSie_dpwiIZ_ljXUj6thLDrsUzFmqM3iOknfPy7245CZY3UvysEHPdUCeSi9pA5-_vmlw03JeS7mpkVt5alDSeE-kEHitWAqhD_z480eZBXq5hJjN_389wOoObLXH8FvT04WEeGYYDgkS7TX_F-oQA_N1E0uHW2cUeRYfRFYQJavWohYrmUUAkn_9wsasWC3ORcBfmvNe6GIvB4Dvft6p_biHrImRiWRFhjf94ibvmyL_Wqi3MWQJ6fdpWmPZnCBR2oqhSuz5bTlmvvPF2l2-S1d6FNWSfr5kBzz7b_B0Co3UStRsZYBgQnE2hZdGnwl8IDCFBxuvXlTCn8-lML3zVd_nybKwVqROb4W2iEsIDfZ4Uv-p7kIwsTZ72KhTv9mszVRV3RcS5ozstzTy91A8IIiG0iCA09vUtXhsqxJUPhTLfkUFIKVB6Sut5lJ_VBxXchuSGyC-rCuu1CJRm1OkAxZ5Hly5ufmowbDVMQYvRhkU2ykxvtBUXccZ07oSLKqsLuuAA\",\r\n    \"txnId\": \"{{TxnId}}\",\r\n    \"villageCode\": \"\",\r\n    \"wardCode\": \"\",\r\n    \"yearOfBirth\": 1998\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/registration/mobile/createHealthId"}, "response": []}], "id": "8c2291e0-532b-4a93-9f61-cb45d7b5969b"}, {"name": "Search", "item": [{"name": "/existsByHealthId", "id": "14665c9a-0f01-4054-be79-6a3d9ca2a26a", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"healthId\": \"deepakndhm\"\r\n}"}, "url": {"raw": "{{BaseURI}}/{{BasePath}}/v1/search/existsByHealthId?phrAddress=", "host": ["{{BaseURI}}"], "path": ["{{BasePath}}", "v1", "search", "existsByHealthId"], "query": [{"key": "phr<PERSON><PERSON><PERSON>", "value": ""}]}}, "response": []}, {"name": "/searchByHealthId", "id": "d1756113-7fe7-48d5-be8d-6ba0fd7331c5", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"healthId\": \"91-**************\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/search/searchByHealthId"}, "response": []}, {"name": "/searchByMobile", "id": "68450962-ae6c-4181-b1c7-baa2e39ab419", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \"gender\": \"M\", \"mobile\": **********, \"name\": \"suraj singh karki\", \"yearOfBirth\": 1994}"}, "url": "{{BaseURI}}/{{BasePath}}/v1/search/searchByMobile"}, "response": []}], "id": "bc0c11db-6173-4674-a6a1-943c0bcecdf9"}, {"name": "LoginWithMobileNumber", "item": [{"name": "/registration/mobile/login/generateOtp", "id": "54b10c9a-69f2-4b37-8ef4-8f3daedfd428", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"mobile\": **********}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/registration/mobile/login/generateOtp"}, "response": []}, {"name": "/registration/mobile/login/resendOtp", "id": "fb2514f1-7ed0-4da4-9fcf-cdce513c822c", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"transactionId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "https://healthidbeta.ndhm.gov.in/api/v2/registration/mobile/login/resendOtp"}, "response": []}, {"name": "/registration/mobile/login/verifyOtp", "id": "9fbd0dfb-e250-473b-a87b-3716f7023cba", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"otp\": \"sw1uD+gpv3fj6NHBNhtcII3GksVtkLT9bvcz0svYDyUt/x3jTtedXSYgw4b90GTwfLfs1eow056VsOw9HFS/wB8uH5Ysx+QzpL7PxmAY1WOHwOj04sPKN6Dw8XY8vcXovtvZc1dUB+TPAlGGPNu8iqMVPetukysjRxgbNdLLKMxn46rIRb8NieeyuDx1EHa90jJP9KwKGZdsLr08BysrmMJExzTO9FT93CzoNg50/nxzaQgmkBSbu9D8DxJm7XrLzWSUB05YCknHbokm4iXwyYBsrmfFDE/xCDfzYPhYyhtEmOi4J/GMp+lO+gAHQFQtxkIADhoSR8WXGcAbCUj7uTjFsBU/tc+RtvSotso4FXy8v+Ylzj28jbFTmmOWyAwYi9pThQjXnmRnq43dVdd5OXmxIII6SXs0JzoFvKwSk7VxhuLIRYzKqrkfcnWMrrmRgE8xZ6ZLft6O3IeiHb9WA8b/6/qO8Hdd17FKsSF6te59gSpoajS0FtQIgFn/c+NHzQYo5ZdsuRGM9v+bhHTInI=\", \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "https://healthidbeta.ndhm.gov.in/api/v2/registration/mobile/login/verifyOtp"}, "response": []}, {"name": "/registration/mobile/login/userAuthorizedToken", "id": "2332aed2-2559-40f0-b617-c57ddf725639", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "T-Token", "value": "Bearer T-Token"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"healthId\": \"72-2054-3723-8510\", \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\"}"}, "url": "https://healthidbeta.ndhm.gov.in/api/v2/registration/mobile/login/userAuthorizedToken"}, "response": []}], "id": "1d73ade1-4da1-4790-b51d-5ed8f5a535f9"}, {"name": "RegistrationwithOtherIDDocuments(Driving Licence, PAN Card, Passport", "item": [{"name": "/document/generate/mobile/otp", "id": "4adab06b-e763-44bc-9b1a-89e6d3a9e3db", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}, {"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"mobile\": **********\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/document/generate/mobile/otp"}, "response": []}, {"name": "/api/v2/document/verify/mobile/otp", "id": "74b4b105-1207-4238-b791-d79c1ef27923", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": \"sw1uD+gpv3fj6NHBNhtcII3GksVtkLT9bvcz0svYDyUt/x3jTtedXSYgw4b90GTwfLfs1eow056VsOw9HFS/wB8uH5Ysx+QzpL7PxmAY1WOHwOj04sPKN6Dw8XY8vcXovtvZc1dUB+TPAlGGPNu8iqMVPetukysjRxgbNdLLKMxn46rIRb8NieeyuDx1EHa90jJP9KwKGZdsLr08BysrmMJExzTO9FT93CzoNg50/nxzaQgmkBSbu9D8DxJm7XrLzWSUB05YCknHbokm4iXwyYBsrmfFDE/xCDfzYPhYyhtEmOi4J/GMp+lO+gAHQFQtxkIADhoSR8WXGcAbCUj7uTjFsBU/tc+RtvSotso4FXy8v+Ylzj28jbFTmmOWyAwYi9pThQjXnmRnq43dVdd5OXmxIII6SXs0JzoFvKwSk7VxhuLIRYzKqrkfcnWMrrmRgE8xZ6ZLft6O3IeiHb9WA8b/6/qO8Hdd17FKsSF6te59gSpoajS0FtQIgFn/c+NHzQYo5ZdsuRGM9v+bhHTInI=\",\r\n    \"txnId\": \"b8fd5665-32fc-47c1-9a7a-f30ed180bb92\"\r\n}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/document/verify/mobile/otp"}, "response": []}, {"name": "/api/v2/document/validate", "id": "d0511230-c030-4e03-bd1a-b2657d386d42", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"dayOfBirth\": 21, \"documentNumber\": \"UK0720190567\", \"documentType\": \"DRIVING_LICENCE\", \"firstName\": \"Deepak\", \"gender\": \"M\", \"lastName\": \"Pant\", \"middleName\": \"<PERSON>\", \"monthOfBirth\": \"03\", \"yearOfBirth\": 1990}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/document/validate"}, "response": []}, {"name": "/api/v2/document", "id": "8012369a-4137-4233-81d3-fff0ed1c5c83", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Accept-Language", "value": "en-US"}, {"key": "T-Token", "value": "Bearer T-Token"}, {"key": "Content-Type", "value": "application/json"}, {"key": "", "value": "Bearer {{AccessToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \"address\": \"Street No 3, Opp. gali no 2\", \"dayOfBirth\": \"04\", \"districtCode\": 401, \"documentNumber\": \"UK0720190567\", \"documentType\": \"DRIVING_LICENCE\", \"firstName\": \"Deepak\", \"gender\": \"M\", \"lastName\": \"Pant\", \"middleName\": \"<PERSON>\", \"mobile\": **********, \"monthOfBirth\": \"03\", \"photo\": \"/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkJCQkJCQoLCwoODw0PDhQSERESFB4WFxYXFh4uHSEdHSEdLikxKCUoMSlJOTMzOUlUR0NHVGZbW2aBeoGoqOIBCQkJCQkJCgsLCg4PDQ8OFBIRERIUHhYXFhcWHi4dIR0dIR0uKTEoJSgxKUk5MzM5SVRHQ0dUZltbZoF6gaio4v/CABEIBLAHgAMBIgACEQEDEQH/xAAbAAACAwEBAQAAAAAAAAAAAAACAwABBAUGB//aAAgBAQAAAADwawLpMspcK7qrlE5F0Vtul2bVywMUNeBHUkW/bmxvYELGuNjh2VDvixxo5ViljKjDRMoahCULjs2JCShjhjh2OGxo0Y2MoXHOLszsKLhw7tD99mpZQxj8xceofmLEKFwXLTIyHwY1Ls+iEotjHY0M0pjRYxtGj4VFKLPohQlFQyy4Qipc0XG9pS+CP/2Q==\", \"photoBack\": \"/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkJCQkJCQoLCwoODw0PDhQSERESFB4WFxYXFh4uHSEdHSEdLikxKCUoMSlJOTMzOUlUR0NHVGZbW2aBeoGoqOIBCQkJCQkJCgsLCg4PDQ8OFBIRERIUHhYXFhcWHi4dIR0dIR0uKTEoJSgxKUk5MzM5SVRHQ0dUZltbZoF6gaio4v/CABEIBLAHgAMBIgACEQEDEQH/xAAbAAACAwEBAQAAAAAAAAAAAAACAwABBAUGB//aAAgBAQAAAADwawLpMspcK7qrlE5F0Vtul2bVywMUNeBHUkW/bmxvYELGuNjh2VDvixxo5ViljKjDRMoahCULjs2JCShjhjh2OGxo0Y2MoXHOLszsKLhw7tD99mpZQxj8xceofmLEKFwXLTIyHwY1Ls+iEotjHY0M0pjRYxtGj4VFKLPohQlFQyy4Qipc0XG9pS+CP/2Q==\", \"stateCode\": 27, \"txnId\": \"a825f76b-0696-40f3-864c-5a3a5b389a83\", \"yearOfBirth\": 1990}"}, "url": "{{BaseURI}}/{{BasePath}}/v2/document"}, "response": []}], "id": "59df71fd-730e-4695-8d16-dfc833656239"}, {"name": "Utility", "item": [{"name": "/api/v2/ha/lgd/states", "id": "231944e6-4818-47d6-b826-40d3ae4091f4", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}, {"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer {{AccessToken}}", "type": "text"}], "url": "{{BaseURI}}/{{BasePath}}/v2/ha/lgd/states"}, "response": []}, {"name": "api/v1/ha/lgd/districts?stateCode=27", "id": "9cbeb21d-672e-40fc-8ad5-21e783061fe4", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}], "url": {"raw": "https://healthidsbx.abdm.gov.in/api/v1/ha/lgd/districts?stateCode=27", "protocol": "https", "host": ["healthidsbx", "abdm", "gov", "in"], "path": ["api", "v1", "ha", "lgd", "districts"], "query": [{"key": "stateCode", "value": "27"}]}}, "response": []}, {"name": "https://healthidsbx.abdm.gov.in/api/v1/ha/lgd/states", "id": "b9137086-919a-47b1-a6ae-4c350274c0ae", "request": {"auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "method": "GET", "header": [{"key": "accept", "value": "*/*"}, {"key": "Accept-Language", "value": "en-US"}], "url": "https://healthidsbx.abdm.gov.in/api/v1/ha/lgd/states"}, "response": []}], "id": "f9afbede-ff4d-4745-b44e-21674d11aed7"}], "id": "e944a225-58a0-4d3c-827f-e6182fd7b392", "auth": {"type": "bearer", "bearer": {"token": "{{AccessToken}}"}}, "event": [{"listen": "prerequest", "script": {"id": "e5d06ba5-bf19-4dd8-8a59-a9e9d267b9cf", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "65ce2d35-8333-4b9f-ba09-375a08247937", "type": "text/javascript", "exec": [""]}}]}]}