/**
 * ABDM Service for ABHA number generation, verification, and management
 * Handles all ABDM API interactions including authentication, session management, and API calls
 */

const { logError, logInfo } = require('../common/logging')
const ABDMErrorHandler = require('../common/abdm-error-handler')
const NodeCache = require('node-cache')
const cache = new NodeCache({ checkperiod: 600 })
const crypto = require('crypto')
const axios = require('axios')

class ABDMService {
  constructor() {
    // ABDM uses different base URLs for different operations
    this.baseUrl =
      process.env.ABDM_BASE_URL || 'https://abhasbx.abdm.gov.in/abha/api/v3'
    this.authUrl =
      process.env.ABDM_AUTH_URL || 'https://dev.abdm.gov.in/api/hiecm/gateway'
    this.sessionUrl =
      process.env.ABDM_SESSION_URL ||
      'https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions'
    this.clientId = process.env.ABDM_CLIENT_ID || 'SBXID_009193'
    this.clientSecret =
      process.env.ABDM_CLIENT_SECRET || '4df7c42b-46c0-4b1b-95b6-35c6a5317708'
    this.sessionCacheKey = 'abdm_session_token'
    this.publicKeyCacheKey = 'abdm_public_key'
  }

  /**
   * Get ABDM public key for encryption
   * @returns {string} - Public key in PEM format
   */
  async getPublicKey() {
    try {
      // Check if we have a cached valid public key
      let publicKey = cache.get(this.publicKeyCacheKey)
      if (publicKey) {
        logInfo('Using cached ABDM public key')
        return publicKey
      }

      // Fetch new public key
      logInfo('Fetching new ABDM public key')
      const sessionToken = await this.getSessionToken()

      const response = await axios.get(
        `${this.baseUrl}/profile/public/certificate`,
        {
          headers: {
            'REQUEST-ID': this.generateRequestId(),
            TIMESTAMP: new Date().toISOString(),
            Authorization: `Bearer ${sessionToken}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data && response.data.publicKey) {
        // Cache the public key for 1 hour
        cache.set(this.publicKeyCacheKey, response.data.publicKey, 3600)
        logInfo('ABDM public key fetched and cached successfully')
        return response.data.publicKey
      }

      throw new Error('Public key not found in response')
    } catch (error) {
      logError('Failed to fetch ABDM public key', {
        error: error.message,
        response: error.response?.data,
      })
      throw error
    }
  }

  /**
   * Encrypt data using ABDM public key
   * @param {string} data - Data to encrypt
   * @returns {string} - Encrypted data in base64 format
   */
  async encryptData(data) {
    try {
      const publicKey = await this.getPublicKey()

      // Convert PEM to proper format if needed
      let formattedKey = publicKey
      if (!publicKey.includes('-----BEGIN')) {
        formattedKey = `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----`
      }

      // Encrypt using RSA-OAEP
      const encrypted = crypto.publicEncrypt(
        {
          key: formattedKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256',
        },
        Buffer.from(data, 'utf8'),
      )

      return encrypted.toString('base64')
    } catch (error) {
      logError('Failed to encrypt data', {
        error: error.message,
        dataLength: data?.length,
      })
      throw error
    }
  }

  /**
   * Generate transaction ID
   * @returns {string} - UUID v4 transaction ID
   */
  generateTransactionId() {
    return require('uuid').v4()
  }

  /**
   * Get or refresh ABDM session token
   * @returns {string} - Session token
   */
  async getSessionToken() {
    try {
      // Check if we have a cached valid token
      let sessionToken = cache.get(this.sessionCacheKey)
      if (sessionToken) {
        logInfo('Using cached ABDM session token')
        return sessionToken
      }

      // Generate new session token using correct ABDM session endpoint
      logInfo('Generating new ABDM session token')
      logInfo(`Using session endpoint: ${this.sessionUrl}`)

      const sessionResponse = await axios.post(
        this.sessionUrl,
        {
          clientId: this.clientId,
          clientSecret: this.clientSecret,
          grantType: 'client_credentials',
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'REQUEST-ID': this.generateRequestId(),
            TIMESTAMP: new Date().toISOString(),
            'X-CM-ID': 'sbx',
          },
          timeout: 10000, // 10 second timeout
        },
      )

      if (!sessionResponse.data) {
        throw new Error('No response data from ABDM session endpoint')
      }

      // Handle different possible response formats
      let accessToken = null
      if (sessionResponse.data.accessToken) {
        accessToken = sessionResponse.data.accessToken
      } else if (sessionResponse.data.token) {
        accessToken = sessionResponse.data.token
      } else if (sessionResponse.data.authToken) {
        accessToken = sessionResponse.data.authToken
      } else if (typeof sessionResponse.data === 'string') {
        accessToken = sessionResponse.data
      }

      if (accessToken) {
        sessionToken = accessToken
        // Cache token for 15 minutes (ABDM tokens typically expire in 20 minutes)
        cache.set(this.sessionCacheKey, sessionToken, 900)
        logInfo('ABDM session token generated and cached successfully')
        return sessionToken
      } else {
        logError('ABDM session response:', sessionResponse.data)
        throw new Error('Failed to extract access token from ABDM response')
      }
    } catch (error) {
      logError('Error getting ABDM session token:', error)
      throw new Error(`ABDM authentication failed: ${error.message}`)
    }
  }

  /**
   * Make authenticated request to ABDM API
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} data - Request data
   * @param {Object} headers - Additional headers
   * @returns {Object} - API response
   */
  async makeAuthenticatedRequest(
    endpoint,
    method = 'POST',
    data = null,
    headers = {},
  ) {
    try {
      // Try to get session token, but continue without it if it fails
      let sessionToken = null
      try {
        sessionToken = await this.getSessionToken()
        logInfo('Session token obtained successfully', sessionToken)
      } catch (authError) {
        logInfo('Session token generation failed, attempting direct API call')
        logError('Auth error:', authError.message)
      }

      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'REQUEST-ID': this.generateRequestId(),
          TIMESTAMP: new Date().toISOString(),
          'X-CM-ID': 'sbx',
          ...headers,
        },
      }

      // Add authorization header only if we have a token
      if (sessionToken) {
        config.headers.Authorization = `Bearer ${sessionToken}`
      } else {
        // For direct API calls without session token, add client credentials
        config.headers['X-ClientId'] = this.clientId
        config.headers['X-ClientSecret'] = this.clientSecret
      }

      if (
        data &&
        (method === 'POST' || method === 'PUT' || method === 'PATCH')
      ) {
        config.data = data
      }

      const response = await axios(config)
      return response.data
    } catch (error) {
      logError(`ABDM API request failed for ${endpoint}:`, error)

      // If unauthorized, clear cached token and retry once
      if (error.response && error.response.status === 401) {
        logInfo('ABDM token expired, clearing cache and retrying')
        cache.del(this.sessionCacheKey)

        // Retry once with new token
        const sessionToken = await this.getSessionToken()
        const config = {
          method,
          url: `${this.baseUrl}${endpoint}`,
          headers: {
            Authorization: `Bearer ${sessionToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...headers,
          },
        }

        if (
          data &&
          (method === 'POST' || method === 'PUT' || method === 'PATCH')
        ) {
          config.data = data
        }

        const retryResponse = await axios(config)
        return retryResponse.data
      }

      throw error
    }
  }

  /**
   * Generate transaction ID for ABDM operations
   * @returns {string} - Transaction ID
   */
  generateTransactionId() {
    return crypto.randomUUID()
  }

  /**
   * Generate request ID for ABDM operations
   * @returns {string} - Request ID
   */
  generateRequestId() {
    return crypto.randomUUID()
  }

  /**
   * Initiate ABHA number creation using Aadhaar
   * @param {string} aadhaar - Aadhaar number
   * @param {string} mobile - Mobile number (optional)
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByAadhaar(aadhaar, mobile = null) {
    const operation = 'initiateAbhaCreationByAadhaar'

    try {
      ABDMErrorHandler.logOperationStart(operation, {
        aadhaar: '***masked***',
        mobile,
      })

      // Encrypt the Aadhaar number as required by ABDM API
      const encryptedAadhaar = await this.encryptData(aadhaar)

      const requestData = {
        txnId: '',
        scope: ['abha-enrol'],
        loginHint: 'aadhaar',
        loginId: encryptedAadhaar,
        otpSystem: 'aadhaar',
      }

      const endpoint = '/enrollment/request/otp'
      logInfo(`Using ABDM Aadhaar enrollment endpoint: ${endpoint}`)

      const response = await this.makeAuthenticatedRequest(
        endpoint,
        'POST',
        requestData,
      )

      logInfo('✅ Successfully initiated ABHA creation by Aadhaar')

      ABDMErrorHandler.logSuccess(operation, {
        txnId: response.data?.txnId,
      })
      return {
        success: true,
        data: response.data,
        txnId: response.data?.txnId,
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      const errorResponse = ABDMErrorHandler.handleError(error, operation, {
        aadhaar: '***masked***',
        mobile,
      })
      return ABDMErrorHandler.sanitizeErrorResponse(errorResponse)
    }
  }

  /**
   * Initiate ABHA number creation using Mobile
   * @param {string} mobile - Mobile number
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByMobile(mobile) {
    try {
      logInfo(`Initiating ABHA creation by mobile: ${mobile}`)

      // Encrypt the mobile number as required by ABDM API
      const encryptedMobile = await this.encryptData(mobile)

      const requestData = {
        scope: ['abha-enrol', 'mobile-verify'],
        loginHint: 'mobile',
        loginId: encryptedMobile,
        otpSystem: 'abdm',
      }

      const endpoint = '/enrollment/request/otp'
      logInfo(`Using ABDM mobile enrollment endpoint: ${endpoint}`)

      const response = await this.makeAuthenticatedRequest(
        endpoint,
        'POST',
        requestData,
      )

      logInfo('✅ Successfully initiated ABHA creation by mobile')

      return {
        success: true,
        data: response.data,
        txnId: response.data?.txnId,
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      logError('ABDM initiateAbhaCreationByMobile failed', {
        operation: 'initiateAbhaCreationByMobile',
        timestamp: new Date().toISOString(),
        context: { mobile: '***masked***' },
        error: {
          message: error.message,
          name: error.name,
          stack: error.stack,
        },
      })

      if (error.response) {
        logError('ABDM HTTP Error - initiateAbhaCreationByMobile', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers,
        })

        return {
          success: false,
          error: 'ABDM API Error',
          details: {
            httpStatus: error.response.status,
            abdmError: error.response.data,
            operation: 'initiateAbhaCreationByMobile',
          },
          timestamp: new Date().toISOString(),
        }
      }

      return {
        success: false,
        error: error.message || 'Unknown error occurred',
        details: {
          operation: 'initiateAbhaCreationByMobile',
        },
        timestamp: new Date().toISOString(),
      }
    }
  }

  /**
   * Verify OTP for ABHA creation (Aadhaar)
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status
   */
  async verifyOtpForAbhaCreation(txnId, otp) {
    try {
      logInfo(`Verifying Aadhaar OTP for ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        otp: otp,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/verifyOTP',
        'POST',
        requestData,
      )

      logInfo('Aadhaar OTP verification for ABHA creation successful')
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP verified successfully',
        data: response,
      }
    } catch (error) {
      logError('Error verifying Aadhaar OTP for ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP for mobile ABHA creation
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status
   */
  async verifyMobileOtpForAbhaCreation(txnId, otp) {
    try {
      logInfo(`Verifying mobile OTP for ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        otp: otp,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/mobile/verifyOtp',
        'POST',
        requestData,
      )

      logInfo('Mobile OTP verification for ABHA creation successful')
      return {
        success: true,
        txnId: response.txnId,
        message: 'Mobile OTP verified successfully',
        data: response,
      }
    } catch (error) {
      logError('Error verifying mobile OTP for ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Complete ABHA number creation (Aadhaar)
   * @param {string} txnId - Transaction ID from OTP verification
   * @param {Object} profileData - User profile data
   * @returns {Object} - Response with ABHA number
   */
  async completeAbhaCreation(txnId, profileData) {
    try {
      logInfo(`Completing Aadhaar ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        ...profileData,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/createHealthIdWithPreVerified',
        'POST',
        requestData,
      )

      logInfo('Aadhaar ABHA creation completed successfully')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError('Error completing Aadhaar ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Complete mobile ABHA number creation
   * @param {string} txnId - Transaction ID from OTP verification
   * @param {Object} profileData - User profile data
   * @returns {Object} - Response with ABHA number
   */
  async completeMobileAbhaCreation(txnId, profileData) {
    try {
      logInfo(`Completing mobile ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        ...profileData,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/mobile/createHealthId',
        'POST',
        requestData,
      )

      logInfo('Mobile ABHA creation completed successfully')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'Mobile ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError('Error completing mobile ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Fetch ABHA details by ABHA number
   * @param {string} abhaNumber - ABHA number
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByNumber(abhaNumber) {
    try {
      logInfo(`Fetching ABHA details for number: ${abhaNumber}`)

      const requestData = {
        healthIdNumber: abhaNumber,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/account/profile/getProfile',
        'POST',
        requestData,
      )

      logInfo('ABHA details fetched successfully')
      return {
        success: true,
        data: response,
        message: 'ABHA details retrieved successfully',
      }
    } catch (error) {
      logError('Error fetching ABHA details:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Fetch ABHA details by mobile number
   * @param {string} mobile - Mobile number
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByMobile(mobile) {
    try {
      logInfo(`Fetching ABHA details for mobile: ${mobile}`)

      const requestData = {
        mobile: mobile,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/account/profile/getProfileByMobile',
        'POST',
        requestData,
      )

      logInfo('ABHA details fetched by mobile successfully')
      return {
        success: true,
        data: response,
        message: 'ABHA details retrieved successfully',
      }
    } catch (error) {
      logError('Error fetching ABHA details by mobile:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify ABHA number
   * @param {string} abhaNumber - ABHA number to verify
   * @returns {Object} - Verification result
   */
  async verifyAbhaNumber(abhaNumber) {
    try {
      logInfo(`Verifying ABHA number: ${abhaNumber}`)

      const requestData = {
        healthIdNumber: abhaNumber,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/verification/healthid',
        'POST',
        requestData,
      )

      logInfo('ABHA number verification completed')
      return {
        success: true,
        isValid: response.status === 'ACTIVE',
        status: response.status,
        data: response,
        message: 'ABHA number verification completed',
      }
    } catch (error) {
      logError('Error verifying ABHA number:', error)
      return {
        success: false,
        isValid: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Resend OTP for ABHA operations
   * @param {string} txnId - Transaction ID
   * @returns {Object} - Response
   */
  async resendOtp(txnId) {
    try {
      logInfo(`Resending OTP for txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/resendAadhaarOtp',
        'POST',
        requestData,
      )

      logInfo('OTP resent successfully')
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP resent successfully',
        data: response,
      }
    } catch (error) {
      logError('Error resending OTP:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }
}

module.exports = new ABDMService()
