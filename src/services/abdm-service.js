/**
 * ABDM Service for ABHA number generation, verification, and management
 * Handles all ABDM API interactions including authentication, session management, and API calls
 */

const { logError, logInfo } = require('../common/logging')
const ABDMErrorHandler = require('../common/abdm-error-handler')
const NodeCache = require('node-cache')
const cache = new NodeCache({ checkperiod: 600 })
const crypto = require('crypto')
const axios = require('axios')

class ABDMService {
  constructor() {
    // ABDM uses different base URLs for different operations
    this.baseUrl =
      process.env.ABDM_BASE_URL || 'https://abhasbx.abdm.gov.in/abha/api/v3'
    this.authUrl =
      process.env.ABDM_AUTH_URL || 'https://dev.abdm.gov.in/api/hiecm/gateway'
    this.sessionUrl =
      process.env.ABDM_SESSION_URL ||
      'https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions'
    this.clientId = process.env.ABDM_CLIENT_ID || 'SBXID_009193'
    this.clientSecret =
      process.env.ABDM_CLIENT_SECRET || '4df7c42b-46c0-4b1b-95b6-35c6a5317708'
    this.sessionCacheKey = 'abdm_session_token'
    this.publicKeyCacheKey = 'abdm_public_key'
  }

  /**
   * Get ABDM public key for encryption
   * @returns {string} - Public key in PEM format
   */
  async getPublicKey() {
    try {
      // Check if we have a cached valid public key
      let publicKey = cache.get(this.publicKeyCacheKey)
      if (publicKey) {
        logInfo('Using cached ABDM public key')
        return publicKey
      }

      // Fetch new public key
      logInfo('Fetching new ABDM public key')
      const sessionToken = await this.getSessionToken()

      const response = await axios.get(
        `${this.baseUrl}/profile/public/certificate`,
        {
          headers: {
            'REQUEST-ID': this.generateRequestId(),
            TIMESTAMP: new Date().toISOString(),
            Authorization: `Bearer ${sessionToken}`,
            'Content-Type': 'application/json',
          },
        },
      )

      if (response.data) {
        let publicKey = null

        // Handle different response formats
        if (response.data.publicKey) {
          publicKey = response.data.publicKey
        } else if (typeof response.data === 'string') {
          publicKey = response.data
        } else if (response.data.certificate) {
          publicKey = response.data.certificate
        }

        if (publicKey) {
          // Cache the public key for 1 hour
          cache.set(this.publicKeyCacheKey, publicKey, 3600)
          logInfo('ABDM public key fetched and cached successfully')
          return publicKey
        }
      }

      throw new Error('Public key not found in response')
    } catch (error) {
      logError('Failed to fetch ABDM public key', {
        error: error.message,
        response: error.response?.data,
      })
      throw error
    }
  }

  /**
   * Encrypt data using ABDM public key
   * @param {string} data - Data to encrypt
   * @returns {string} - Encrypted data in base64 format
   */
  async encryptData(data) {
    try {
      const publicKeyData = await this.getPublicKey()

      // Use the public key directly from the response
      let publicKey = publicKeyData
      if (typeof publicKeyData === 'object' && publicKeyData.publicKey) {
        publicKey = publicKeyData.publicKey
      }

      // Convert to proper PEM format
      let formattedKey = publicKey
      if (!publicKey.includes('-----BEGIN')) {
        formattedKey = `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----`
      }

      // Encrypt using RSA-OAEP with SHA-1 (as per ABDM specification)
      // ABDM uses RSA/ECB/OAEPWithSHA-1AndMGF1Padding
      const encrypted = crypto.publicEncrypt(
        {
          key: formattedKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha1', // ABDM uses SHA-1, not SHA-256
        },
        Buffer.from(data, 'utf8'),
      )

      return encrypted.toString('base64')
    } catch (error) {
      logError('Failed to encrypt data', {
        error: error.message,
        dataLength: data?.length,
      })
      throw error
    }
  }

  /**
   * Generate transaction ID
   * @returns {string} - UUID v4 transaction ID
   */
  generateTransactionId() {
    return require('uuid').v4()
  }

  /**
   * Get or refresh ABDM session token
   * @returns {string} - Session token
   */
  async getSessionToken() {
    try {
      // Check if we have a cached valid token
      let sessionToken = cache.get(this.sessionCacheKey)
      if (sessionToken) {
        logInfo('Using cached ABDM session token')
        return sessionToken
      }

      // Generate new session token using correct ABDM session endpoint
      logInfo('Generating new ABDM session token')
      logInfo(`Using session endpoint: ${this.sessionUrl}`)

      const sessionResponse = await axios.post(
        this.sessionUrl,
        {
          clientId: this.clientId,
          clientSecret: this.clientSecret,
          grantType: 'client_credentials',
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'REQUEST-ID': this.generateRequestId(),
            TIMESTAMP: new Date().toISOString(),
            'X-CM-ID': 'sbx',
          },
          timeout: 10000, // 10 second timeout
        },
      )

      if (!sessionResponse.data) {
        throw new Error('No response data from ABDM session endpoint')
      }

      // Handle different possible response formats
      let accessToken = null
      if (sessionResponse.data.accessToken) {
        accessToken = sessionResponse.data.accessToken
      } else if (sessionResponse.data.token) {
        accessToken = sessionResponse.data.token
      } else if (sessionResponse.data.authToken) {
        accessToken = sessionResponse.data.authToken
      } else if (typeof sessionResponse.data === 'string') {
        accessToken = sessionResponse.data
      }

      if (accessToken) {
        sessionToken = accessToken
        // Cache token for 15 minutes (ABDM tokens typically expire in 20 minutes)
        cache.set(this.sessionCacheKey, sessionToken, 900)
        logInfo('ABDM session token generated and cached successfully')
        return sessionToken
      } else {
        logError('ABDM session response:', sessionResponse.data)
        throw new Error('Failed to extract access token from ABDM response')
      }
    } catch (error) {
      logError('Error getting ABDM session token:', error)
      throw new Error(`ABDM authentication failed: ${error.message}`)
    }
  }

  /**
   * Make authenticated request to ABDM API
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} data - Request data
   * @param {Object} headers - Additional headers
   * @returns {Object} - API response
   */
  async makeAuthenticatedRequest(
    endpoint,
    method = 'POST',
    data = null,
    headers = {},
  ) {
    try {
      // Try to get session token, but continue without it if it fails
      let sessionToken = null
      try {
        sessionToken = await this.getSessionToken()
        logInfo('Session token obtained successfully:', sessionToken)
      } catch (authError) {
        logInfo('Session token generation failed, attempting direct API call')
        logError('Auth error:', authError.message)
      }

      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'REQUEST-ID': this.generateRequestId(),
          TIMESTAMP: new Date().toISOString(),
          'X-CM-ID': 'sbx',
          ...headers,
        },
      }

      // Add authorization header only if we have a token
      if (sessionToken) {
        config.headers.Authorization = `Bearer ${sessionToken}`
      } else {
        // For direct API calls without session token, add client credentials
        config.headers['X-ClientId'] = this.clientId
        config.headers['X-ClientSecret'] = this.clientSecret
      }

      if (
        data &&
        (method === 'POST' || method === 'PUT' || method === 'PATCH')
      ) {
        config.data = data
        logInfo('Request data:', data)
      }

      const response = await axios(config)
      return response.data
    } catch (error) {
      logError(`ABDM API request failed for ${endpoint}:`, error)

      // If unauthorized, clear cached token and retry once
      if (error.response && error.response.status === 401) {
        logInfo('ABDM token expired, clearing cache and retrying')
        cache.del(this.sessionCacheKey)

        // Retry once with new token
        const sessionToken = await this.getSessionToken()
        const config = {
          method,
          url: `${this.baseUrl}${endpoint}`,
          headers: {
            Authorization: `Bearer ${sessionToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...headers,
          },
        }

        if (
          data &&
          (method === 'POST' || method === 'PUT' || method === 'PATCH')
        ) {
          config.data = data
          logInfo('Retrying with request data:', data)
        }

        const retryResponse = await axios(config)
        return retryResponse.data
      }

      throw error
    }
  }

  /**
   * Generate transaction ID for ABDM operations
   * @returns {string} - Transaction ID
   */
  generateTransactionId() {
    return crypto.randomUUID()
  }

  /**
   * Generate request ID for ABDM operations
   * @returns {string} - Request ID
   */
  generateRequestId() {
    return crypto.randomUUID()
  }

  /**
   * Initiate ABHA number creation using Aadhaar
   * @param {string} aadhaar - Aadhaar number
   * @param {string} mobile - Mobile number (optional)
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByAadhaar(aadhaar, mobile = null) {
    const operation = 'initiateAbhaCreationByAadhaar'

    try {
      ABDMErrorHandler.logOperationStart(operation, {
        aadhaar: '***masked***',
        mobile,
      })

      // Encrypt the Aadhaar number as required by ABDM API
      const encryptedAadhaar = await this.encryptData(aadhaar)

      const requestData = {
        txnId: '',
        scope: ['abha-enrol'],
        loginHint: 'aadhaar',
        loginId: encryptedAadhaar,
        otpSystem: 'aadhaar',
      }
      console.log(requestData, 'requestData&&&&&&&&&&&&&')

      const endpoint = '/enrollment/request/otp'
      logInfo(`Using ABDM Aadhaar enrollment endpoint: ${endpoint}`)

      const response = await this.makeAuthenticatedRequest(
        endpoint,
        'POST',
        requestData,
      )

      logInfo('✅ Successfully initiated ABHA creation by Aadhaar')

      ABDMErrorHandler.logSuccess(operation, {
        txnId: response.data?.txnId,
      })
      return {
        success: true,
        data: response,
        txnId: response.txnId,
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      const errorResponse = ABDMErrorHandler.handleError(error, operation, {
        aadhaar: '***masked***',
        mobile,
      })
      return ABDMErrorHandler.sanitizeErrorResponse(errorResponse)
    }
  }

  /**
   * Initiate ABHA number creation using Mobile
   * @param {string} mobile - Mobile number
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByMobile(mobile) {
    try {
      logInfo(`Initiating ABHA creation by mobile: ${mobile}`)

      // Encrypt the mobile number as required by ABDM API
      const encryptedMobile = await this.encryptData(mobile)

      const requestData = {
        scope: ['abha-enrol', 'mobile-verify', 'dl-flow'],
        loginHint: 'mobile',
        loginId: encryptedMobile,
        otpSystem: 'abdm',
      }

      const endpoint = '/enrollment/request/otp'
      logInfo(`Using ABDM mobile enrollment endpoint: ${endpoint}`)

      const response = await this.makeAuthenticatedRequest(
        endpoint,
        'POST',
        requestData,
      )

      logInfo('✅ Successfully initiated ABHA creation by mobile')
      console.log(response, 'hhhhhhhhh')

      // Ensure txnId is included in the response
      return {
        success: true,
        txnId: response.txnId,
        message: response.message,
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      logError('ABDM initiateAbhaCreationByMobile failed', error)
      return {
        success: false,
        error: error.message || 'Unknown error occurred',
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP for ABHA creation (Aadhaar)
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status
   */
  async verifyOtpForAbhaCreation(txnId, otp, mobile) {
    try {
      logInfo(`Verifying Aadhaar OTP for ABHA creation with txnId: ${txnId}`)

      // Encrypt the OTP using the public key
      const encryptedOtp = await this.encryptData(otp)

      const requestData = {
        authData: {
          authMethods: ['otp'],
          otp: {
            txnId: txnId,
            otpValue: encryptedOtp,
            mobile: mobile,
          },
        },
        consent: {
          code: 'abha-enrollment',
          version: '1.4',
        },
      }

      logInfo('Request payload for OTP verification:', requestData)

      const response = await this.makeAuthenticatedRequest(
        '/enrollment/enrol/byAadhaar',
        'POST',
        requestData,
      )
      console.log(response)
      logInfo('Aadhaar OTP verification for ABHA creation successful')
      return {
        success: true,
        message: 'OTP verified successfully',
        data: response, // Include the full response object here
      }
    } catch (error) {
      logError('Error verifying Aadhaar OTP for ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP for mobile ABHA creation
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status
   */
  async verifyMobileOtpForAbhaCreation(txnId, otp) {
    try {
      logInfo(`Verifying mobile OTP for ABHA creation with txnId: ${txnId}`)

      // Encrypt the OTP using the public key
      const encryptedOtp = await this.encryptData(otp)
      logInfo(`Encrypted OTP: ${encryptedOtp}`)

      const requestData = {
        scope: ['abha-enrol', 'mobile-verify', 'dl-flow'], // Correct scope for mobile OTP verification
        authData: {
          authMethods: ['otp'],
          otp: {
            timeStamp: new Date().toISOString(), // Add current timestamp
            txnId: txnId,
            otpValue: encryptedOtp, // Use encrypted OTP
          },
        },
      }
      console.log('Request payload for OTP verification:', requestData)

      const response = await this.makeAuthenticatedRequest(
        '/enrollment/auth/byAbdm',
        'POST',
        requestData,
      )

      logInfo('Mobile OTP verification for ABHA creation successful')
      return {
        success: true,
        txnId: response.txnId,
        message: 'Mobile OTP verified successfully',
        data: response,
      }
    } catch (error) {
      logError('Error verifying mobile OTP for ABHA creation:', error)

      // Log response details if available
      if (error.response) {
        logError('API Response:', error.response.data)
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Complete ABHA number creation (Aadhaar)
   * @param {string} txnId - Transaction ID from OTP verification
   * @param {Object} profileData - User profile data
   * @returns {Object} - Response with ABHA number
   */
  async completeAbhaCreation(txnId, profileData) {
    try {
      logInfo(`Completing Aadhaar ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        ...profileData,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/createHealthIdWithPreVerified',
        'POST',
        requestData,
      )

      logInfo('Aadhaar ABHA creation completed successfully')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError('Error completing Aadhaar ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Complete mobile ABHA number creation
   * @param {string} txnId - Transaction ID from OTP verification
   * @param {Object} profileData - User profile data
   * @returns {Object} - Response with ABHA number
   */
  async completeMobileAbhaCreation(txnId, profileData) {
    try {
      logInfo(`Completing mobile ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        ...profileData,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/mobile/createHealthId',
        'POST',
        requestData,
      )

      logInfo('Mobile ABHA creation completed successfully')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'Mobile ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError('Error completing mobile ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Fetch ABHA details by ABHA number
   * @param {string} abhaNumber - ABHA number
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByNumber(abhaNumber) {
    try {
      logInfo(`Fetching ABHA details for number: ${abhaNumber}`)

      const encryptedAbhaNumber = await this.encryptData(abhaNumber)

      const requestData = {
        scope: ['abha-login', 'aadhaar-verify'],
        loginHint: 'abha-number',
        loginId: encryptedAbhaNumber,
        otpSystem: 'aadhaar',
      }

      logInfo('Request payload for fetching ABHA details:', requestData)

      const response = await this.makeAuthenticatedRequest(
        '/profile/login/request/otp',
        'POST',
        requestData,
      )

      logInfo('ABHA details fetched successfully')
      return {
        success: true,
        data: response,
        message: 'ABHA details retrieved successfully',
      }
    } catch (error) {
      logError('Error fetching ABHA details:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Fetch ABHA details by mobile number
   * @param {string} mobile - Mobile number
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByMobile(mobile) {
    try {
      logInfo(`Fetching ABHA details for mobile: ${mobile}`)

      // Encrypt the mobile number as required by ABDM API
      const encryptedMobile = await this.encryptData(mobile)

      const requestData = {
        scope: ['abha-login', 'mobile-verify'],
        loginHint: 'mobile',
        loginId: encryptedMobile,
        otpSystem: 'abdm',
      }

      logInfo(
        'Request payload for fetching ABHA details by mobile:',
        requestData,
      )

      const response = await this.makeAuthenticatedRequest(
        '/profile/login/request/otp',
        'POST',
        requestData,
      )

      logInfo('ABHA details fetched successfully by mobile')
      return {
        success: true,
        data: response,
        message: 'ABHA details retrieved successfully',
      }
    } catch (error) {
      logError('Error fetching ABHA details by mobile:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify ABHA number
   * @param {string} abhaNumber - ABHA number to verify
   * @returns {Object} - Verification result
   */
  async verifyAbhaNumber(abhaNumber) {
    try {
      logInfo(`Verifying ABHA number: ${abhaNumber}`)

      const requestData = {
        healthIdNumber: abhaNumber,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/verification/healthid',
        'POST',
        requestData,
      )

      logInfo('ABHA number verification completed')
      return {
        success: true,
        isValid: response.status === 'ACTIVE',
        status: response.status,
        data: response,
        message: 'ABHA number verification completed',
      }
    } catch (error) {
      logError('Error verifying ABHA number:', error)
      return {
        success: false,
        isValid: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Resend OTP for ABHA operations
   * @param {string} txnId - Transaction ID
   * @returns {Object} - Response
   */
  async resendOtp(txnId) {
    try {
      logInfo(`Resending OTP for txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/resendAadhaarOtp',
        'POST',
        requestData,
      )

      logInfo('OTP resent successfully')
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP resent successfully',
        data: response,
      }
    } catch (error) {
      logError('Error resending OTP:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP and create ABHA number using Aadhaar
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @param {string} mobile - Mobile number for ABHA communication
   * @returns {Object} - Response with ABHA number and details
   */
  async verifyOtpAndCreateAbhaByAadhaar(txnId, otp, mobile) {
    try {
      logInfo(
        `Verifying OTP and creating ABHA number using Aadhaar with txnId: ${txnId}`,
      )

      // Encrypt the OTP using the public key
      const encryptedOtp = await this.encryptData(otp)

      const requestData = {
        authData: {
          authMethods: ['otp'],
          otp: {
            txnId: txnId,
            otpValue: encryptedOtp,
            mobile: mobile,
          },
        },
        consent: {
          code: 'abha-enrollment',
          version: '1.4',
        },
      }
      logInfo(
        'Request payload for OTP verification and ABHA creation:',
        requestData,
      )
      const response = await this.makeAuthenticatedRequest(
        '/enrollment/enrol/byAadhaar',
        'POST',
        requestData,
      )

      logInfo('ABHA number created successfully using Aadhaar')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError(
        'Error verifying OTP and creating ABHA number using Aadhaar:',
        error,
      )

      // Log response details if available
      if (error.response) {
        logError('API Response:', error.response.data)
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP and fetch ABHA details by number
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status and ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByNumber(txnId, otp) {
    try {
      logInfo(
        `Verifying OTP and fetching ABHA details by number with txnId: ${txnId}`,
      )

      // Step 1: Encrypt the OTP using the public key
      const encryptedOtp = await this.encryptData(otp)

      const requestData = {
        scope: ['abha-login', 'aadhaar-verify'],
        authData: {
          authMethods: ['otp'],
          otp: {
            txnId: txnId,
            otpValue: encryptedOtp,
          },
        },
      }
      logInfo(
        'Request payload for OTP verification and fetching ABHA details:',
        requestData,
      )

      // Step 2: Call /profile/login/verify to verify OTP and get the token
      const verifyResponse = await this.makeAuthenticatedRequest(
        '/profile/login/verify',
        'POST',
        requestData,
      )

      const xToken = verifyResponse?.token
      if (!xToken) {
        throw new Error(
          'Failed to retrieve token from OTP verification response',
        )
      }

      logInfo('OTP verified successfully, token retrieved:', xToken)

      // Step 3: Use the token as xToken to fetch account details
      const accountDetailsResponse = await this.makeAuthenticatedRequest(
        '/profile/account',
        'GET',
        null,
        {
          'X-token': `Bearer ${xToken}`,
        },
      )

      logInfo('Account details fetched successfully')
      return {
        success: true,
        message: 'OTP verified and ABHA details fetched successfully',
        data: accountDetailsResponse,
      }
    } catch (error) {
      logError(
        'Error verifying OTP and fetching ABHA details by number:',
        error,
      )
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify ABHA details by mobile number
   * @param {string} mobile - Mobile number
   * @returns {Object} - Verification result
   */
  async verifyAbhaDetailsByMobile(txnId, otp) {
    try {
      logInfo(`Verifying ABHA details for mobile with txnId: ${txnId}`)

      // Step 1: Encrypt the OTP using the public key
      const encryptedOtp = await this.encryptData(otp)

      const requestData = {
        scope: ['abha-login', 'mobile-verify'],
        authData: {
          authMethods: ['otp'],
          otp: {
            txnId: txnId,
            otpValue: encryptedOtp,
          },
        },
      }
      console.log(requestData, 'requestData')

      logInfo(
        'Request payload for verifying ABHA details by mobile:',
        requestData,
      )

      // Step 2: Call /profile/login/verify to verify OTP and get the token
      const verifyResponse = await this.makeAuthenticatedRequest(
        '/profile/login/verify',
        'POST',
        requestData,
      )
      console.log('verifyResponse********', verifyResponse)
      console.log('verifyResponse********', verifyResponse.token)
      let xToken = verifyResponse?.token
      if (!xToken) {
        throw new Error(
          'Failed to retrieve token from OTP verification response',
        )
      }

      logInfo('OTP verified successfully, token retrieved:', xToken)

      // Step 3: Decode and validate the X-token expiry
      const decodedToken = JSON.parse(
        Buffer.from(xToken.split('.')[1], 'base64').toString('utf8'),
      )
      const currentTime = Math.floor(Date.now() / 1000)
      if (decodedToken.exp <= currentTime) {
        logInfo('X-token has expired. Re-verifying OTP to fetch a new token.')
        throw new Error('X-token expired')
      }

      // Step 4: Use the token as X-token to fetch account details
      try {
        const requestId = this.generateRequestId() // Generate a unique REQUEST-ID
        logInfo(`Generated REQUEST-ID: ${requestId}`)

        const accountDetailsResponse = await this.makeAuthenticatedRequest(
          '/profile/account',
          'GET',
          null,
          {
            'X-token': `Bearer ${xToken}`,
            'REQUEST-ID': requestId, // Include unique REQUEST-ID
            TIMESTAMP: new Date().toISOString(), // Ensure correct timestamp
          },
        )

        logInfo('Account details fetched successfully')
        return {
          success: true,
          message: 'OTP verified and ABHA details fetched successfully',
          data: accountDetailsResponse,
        }
      } catch (accountError) {
        if (
          accountError.response &&
          accountError.response.data &&
          accountError.response.data.code === 'ABDM-1094'
        ) {
          logInfo('X-token expired. Re-verifying OTP to fetch a new token.')
          throw new Error('X-token expired')
        }

        logError('Error fetching account details with xToken:', {
          xToken,
          error: accountError.message,
          response: accountError.response?.data,
        })
        throw new Error('Failed to fetch account details. Please try again.')
      }
    } catch (error) {
      if (error.message === 'X-token expired') {
        logInfo('Retrying OTP verification to fetch a new X-token.')
        return await this.verifyAbhaDetailsByMobile(txnId, otp) // Retry OTP verification
      }

      logError(
        'Error verifying OTP and fetching ABHA details by mobile:',
        error,
      )
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }
}

module.exports = new ABDMService()
