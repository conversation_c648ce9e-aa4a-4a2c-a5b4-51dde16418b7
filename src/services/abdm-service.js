/**
 * ABDM Service for ABHA number generation, verification, and management
 * Handles all ABDM API interactions including authentication, session management, and API calls
 */

const { logError, logInfo } = require('../common/logging')
const ABDMErrorHandler = require('../common/abdm-error-handler')
const NodeCache = require('node-cache')
const cache = new NodeCache({ checkperiod: 600 })
const crypto = require('crypto')
const axios = require('axios')

class ABDMService {
  constructor() {
    // ABDM uses different base URLs for different operations
    this.baseUrl =
      process.env.ABDM_BASE_URL || 'https://dev.abdm.gov.in/gateway'
    this.authUrl =
      process.env.ABDM_AUTH_URL || 'https://dev.abdm.gov.in/gateway'
    this.sessionUrl =
      process.env.ABDM_SESSION_URL ||
      'https://dev.abdm.gov.in/gateway/v0.5/sessions'
    this.clientId = process.env.ABDM_CLIENT_ID || 'SBXID_009193'
    this.clientSecret =
      process.env.ABDM_CLIENT_SECRET || '4df7c42b-46c0-4b1b-95b6-35c6a5317708'
    this.sessionCacheKey = 'abdm_session_token'
  }

  /**
   * Get or refresh ABDM session token
   * @returns {string} - Session token
   */
  async getSessionToken() {
    try {
      // Check if we have a cached valid token
      let sessionToken = cache.get(this.sessionCacheKey)
      if (sessionToken) {
        logInfo('Using cached ABDM session token')
        return sessionToken
      }

      // Generate new session token using correct ABDM session endpoint
      logInfo('Generating new ABDM session token')
      logInfo(`Using session endpoint: ${this.sessionUrl}`)

      const sessionResponse = await axios.post(
        this.sessionUrl,
        {
          clientId: this.clientId,
          clientSecret: this.clientSecret,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          timeout: 10000, // 10 second timeout
        },
      )

      if (!sessionResponse.data) {
        throw new Error('No response data from ABDM session endpoint')
      }

      // Handle different possible response formats
      let accessToken = null
      if (sessionResponse.data.accessToken) {
        accessToken = sessionResponse.data.accessToken
      } else if (sessionResponse.data.token) {
        accessToken = sessionResponse.data.token
      } else if (sessionResponse.data.authToken) {
        accessToken = sessionResponse.data.authToken
      } else if (typeof sessionResponse.data === 'string') {
        accessToken = sessionResponse.data
      }

      if (accessToken) {
        sessionToken = accessToken
        // Cache token for 15 minutes (ABDM tokens typically expire in 20 minutes)
        cache.set(this.sessionCacheKey, sessionToken, 900)
        logInfo('ABDM session token generated and cached successfully')
        return sessionToken
      } else {
        logError('ABDM session response:', sessionResponse.data)
        throw new Error('Failed to extract access token from ABDM response')
      }
    } catch (error) {
      logError('Error getting ABDM session token:', error)
      throw new Error(`ABDM authentication failed: ${error.message}`)
    }
  }

  /**
   * Make authenticated request to ABDM API
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} data - Request data
   * @param {Object} headers - Additional headers
   * @returns {Object} - API response
   */
  async makeAuthenticatedRequest(
    endpoint,
    method = 'POST',
    data = null,
    headers = {},
  ) {
    try {
      // Try to get session token, but continue without it if it fails
      let sessionToken = null
      try {
        sessionToken = await this.getSessionToken()
        logInfo('Session token obtained successfully', sessionToken)
      } catch (authError) {
        logInfo('Session token generation failed, attempting direct API call')
        logError('Auth error:', authError.message)
      }

      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          ...headers,
        },
      }

      // Add authorization header only if we have a token
      if (sessionToken) {
        config.headers.Authorization = `Bearer ${sessionToken}`
      } else {
        // For direct API calls without session token, add client credentials
        config.headers['X-ClientId'] = this.clientId
        config.headers['X-ClientSecret'] = this.clientSecret
      }

      if (
        data &&
        (method === 'POST' || method === 'PUT' || method === 'PATCH')
      ) {
        config.data = data
      }

      const response = await axios(config)
      return response.data
    } catch (error) {
      logError(`ABDM API request failed for ${endpoint}:`, error)

      // If unauthorized, clear cached token and retry once
      if (error.response && error.response.status === 401) {
        logInfo('ABDM token expired, clearing cache and retrying')
        cache.del(this.sessionCacheKey)

        // Retry once with new token
        const sessionToken = await this.getSessionToken()
        const config = {
          method,
          url: `${this.baseUrl}${endpoint}`,
          headers: {
            Authorization: `Bearer ${sessionToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...headers,
          },
        }

        if (
          data &&
          (method === 'POST' || method === 'PUT' || method === 'PATCH')
        ) {
          config.data = data
        }

        const retryResponse = await axios(config)
        return retryResponse.data
      }

      throw error
    }
  }

  /**
   * Generate transaction ID for ABDM operations
   * @returns {string} - Transaction ID
   */
  generateTransactionId() {
    return crypto.randomUUID()
  }

  /**
   * Generate request ID for ABDM operations
   * @returns {string} - Request ID
   */
  generateRequestId() {
    return crypto.randomUUID()
  }

  /**
   * Initiate ABHA number creation using Aadhaar
   * @param {string} aadhaar - Aadhaar number
   * @param {string} mobile - Mobile number (optional)
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByAadhaar(aadhaar, mobile = null) {
    const operation = 'initiateAbhaCreationByAadhaar'

    try {
      ABDMErrorHandler.logOperationStart(operation, {
        aadhaar: '***masked***',
        mobile,
      })

      const requestData = {
        aadhaar: aadhaar,
        ...(mobile && { mobile: mobile }),
        txnId: this.generateTransactionId(),
      }

      // Try different possible endpoint paths for Aadhaar registration
      const possibleEndpoints = [
        '/v1/registration/aadhaar/generateOtp',
        '/v0.5/registration/aadhaar/generateOtp',
        '/v2/registration/aadhaar/generateOtp',
        '/registration/aadhaar/generateOtp',
        '/v1/registration/byAadhaar',
        '/v0.5/registration/byAadhaar',
        '/api/v1/registration/aadhaar/generateOtp',
        '/api/v0.5/registration/aadhaar/generateOtp',
      ]

      let response = null
      let lastError = null

      for (const endpoint of possibleEndpoints) {
        try {
          logInfo(`Trying ABDM Aadhaar registration endpoint: ${endpoint}`)
          response = await this.makeAuthenticatedRequest(
            endpoint,
            'POST',
            requestData,
          )
          logInfo(
            `✅ Successfully connected to ABDM Aadhaar endpoint: ${endpoint}`,
          )
          break
        } catch (error) {
          lastError = error
          logInfo(
            `❌ Failed to connect to ${endpoint}: ${error.response?.status} ${error.response?.statusText}`,
          )
          continue
        }
      }

      if (!response) {
        logError('All ABDM Aadhaar registration endpoints failed')
        throw (
          lastError ||
          new Error('All ABDM Aadhaar registration endpoints failed')
        )
      }

      ABDMErrorHandler.logSuccess(operation, { txnId: response.txnId })
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP sent successfully',
        data: response,
      }
    } catch (error) {
      const errorResponse = ABDMErrorHandler.handleError(error, operation, {
        aadhaar: '***masked***',
        mobile,
      })
      return ABDMErrorHandler.sanitizeErrorResponse(errorResponse)
    }
  }

  /**
   * Initiate ABHA number creation using Mobile
   * @param {string} mobile - Mobile number
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByMobile(mobile) {
    try {
      logInfo(`Initiating ABHA creation by mobile: ${mobile}`)

      const requestData = {
        mobile: mobile,
        txnId: this.generateTransactionId(),
      }

      // Try different possible endpoint paths for mobile registration
      const possibleEndpoints = [
        '/v1/registration/mobile/generateOtp',
        '/v0.5/registration/mobile/generateOtp',
        '/v2/registration/mobile/generateOtp',
        '/registration/mobile/generateOtp',
        '/v1/registration/byMobile',
        '/v0.5/registration/byMobile',
        '/api/v1/registration/mobile/generateOtp',
        '/api/v0.5/registration/mobile/generateOtp',
      ]

      let response = null
      let lastError = null

      for (const endpoint of possibleEndpoints) {
        try {
          logInfo(`Trying ABDM mobile registration endpoint: ${endpoint}`)
          response = await this.makeAuthenticatedRequest(
            endpoint,
            'POST',
            requestData,
          )
          logInfo(
            `✅ Successfully connected to ABDM mobile endpoint: ${endpoint}`,
          )
          break
        } catch (error) {
          lastError = error
          logInfo(
            `❌ Failed to connect to ${endpoint}: ${error.response?.status} ${error.response?.statusText}`,
          )
          continue
        }
      }

      if (!response) {
        logError('All ABDM mobile registration endpoints failed')
        throw (
          lastError ||
          new Error('All ABDM mobile registration endpoints failed')
        )
      }

      logInfo('ABHA creation by mobile initiated successfully')
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP sent successfully',
        data: response,
      }
    } catch (error) {
      logError('Error initiating ABHA creation by mobile:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP for ABHA creation (Aadhaar)
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status
   */
  async verifyOtpForAbhaCreation(txnId, otp) {
    try {
      logInfo(`Verifying Aadhaar OTP for ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        otp: otp,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/verifyOTP',
        'POST',
        requestData,
      )

      logInfo('Aadhaar OTP verification for ABHA creation successful')
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP verified successfully',
        data: response,
      }
    } catch (error) {
      logError('Error verifying Aadhaar OTP for ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify OTP for mobile ABHA creation
   * @param {string} txnId - Transaction ID from initiation
   * @param {string} otp - OTP received
   * @returns {Object} - Response with verification status
   */
  async verifyMobileOtpForAbhaCreation(txnId, otp) {
    try {
      logInfo(`Verifying mobile OTP for ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        otp: otp,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/mobile/verifyOtp',
        'POST',
        requestData,
      )

      logInfo('Mobile OTP verification for ABHA creation successful')
      return {
        success: true,
        txnId: response.txnId,
        message: 'Mobile OTP verified successfully',
        data: response,
      }
    } catch (error) {
      logError('Error verifying mobile OTP for ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Complete ABHA number creation (Aadhaar)
   * @param {string} txnId - Transaction ID from OTP verification
   * @param {Object} profileData - User profile data
   * @returns {Object} - Response with ABHA number
   */
  async completeAbhaCreation(txnId, profileData) {
    try {
      logInfo(`Completing Aadhaar ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        ...profileData,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/createHealthIdWithPreVerified',
        'POST',
        requestData,
      )

      logInfo('Aadhaar ABHA creation completed successfully')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError('Error completing Aadhaar ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Complete mobile ABHA number creation
   * @param {string} txnId - Transaction ID from OTP verification
   * @param {Object} profileData - User profile data
   * @returns {Object} - Response with ABHA number
   */
  async completeMobileAbhaCreation(txnId, profileData) {
    try {
      logInfo(`Completing mobile ABHA creation with txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
        ...profileData,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/mobile/createHealthId',
        'POST',
        requestData,
      )

      logInfo('Mobile ABHA creation completed successfully')
      return {
        success: true,
        abhaNumber: response.healthIdNumber,
        abhaAddress: response.healthId,
        message: 'Mobile ABHA number created successfully',
        data: response,
      }
    } catch (error) {
      logError('Error completing mobile ABHA creation:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Fetch ABHA details by ABHA number
   * @param {string} abhaNumber - ABHA number
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByNumber(abhaNumber) {
    try {
      logInfo(`Fetching ABHA details for number: ${abhaNumber}`)

      const requestData = {
        healthIdNumber: abhaNumber,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/account/profile/getProfile',
        'POST',
        requestData,
      )

      logInfo('ABHA details fetched successfully')
      return {
        success: true,
        data: response,
        message: 'ABHA details retrieved successfully',
      }
    } catch (error) {
      logError('Error fetching ABHA details:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Fetch ABHA details by mobile number
   * @param {string} mobile - Mobile number
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByMobile(mobile) {
    try {
      logInfo(`Fetching ABHA details for mobile: ${mobile}`)

      const requestData = {
        mobile: mobile,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/account/profile/getProfileByMobile',
        'POST',
        requestData,
      )

      logInfo('ABHA details fetched by mobile successfully')
      return {
        success: true,
        data: response,
        message: 'ABHA details retrieved successfully',
      }
    } catch (error) {
      logError('Error fetching ABHA details by mobile:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Verify ABHA number
   * @param {string} abhaNumber - ABHA number to verify
   * @returns {Object} - Verification result
   */
  async verifyAbhaNumber(abhaNumber) {
    try {
      logInfo(`Verifying ABHA number: ${abhaNumber}`)

      const requestData = {
        healthIdNumber: abhaNumber,
        txnId: this.generateTransactionId(),
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/verification/healthid',
        'POST',
        requestData,
      )

      logInfo('ABHA number verification completed')
      return {
        success: true,
        isValid: response.status === 'ACTIVE',
        status: response.status,
        data: response,
        message: 'ABHA number verification completed',
      }
    } catch (error) {
      logError('Error verifying ABHA number:', error)
      return {
        success: false,
        isValid: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }

  /**
   * Resend OTP for ABHA operations
   * @param {string} txnId - Transaction ID
   * @returns {Object} - Response
   */
  async resendOtp(txnId) {
    try {
      logInfo(`Resending OTP for txnId: ${txnId}`)

      const requestData = {
        txnId: txnId,
      }

      const response = await this.makeAuthenticatedRequest(
        '/v1/registration/aadhaar/resendAadhaarOtp',
        'POST',
        requestData,
      )

      logInfo('OTP resent successfully')
      return {
        success: true,
        txnId: response.txnId,
        message: 'OTP resent successfully',
        data: response,
      }
    } catch (error) {
      logError('Error resending OTP:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        details: error.response?.data,
      }
    }
  }
}

module.exports = new ABDMService()
