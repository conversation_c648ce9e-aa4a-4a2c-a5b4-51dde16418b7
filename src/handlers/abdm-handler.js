/**
 * ABDM Handler for ABHA number generation, verification, and management
 * Handles all ABDM-related operations and API interactions
 */

const abdmService = require('../services/abdm-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { logError, logInfo } = require('../common/logging')
const {
  ABDMInitiateAadhaarRequest,
  ABDMInitiateMobileRequest,
  ABDMVerifyOtpRequest,
  ABDMCompleteCreationRequest,
  ABDMGetDetailsByNumberRequest,
  ABDMGetDetailsByMobileRequest,
  ABDMVerifyNumberRequest,
  ABDMResendOtpRequest,
  ABDMValidator,
} = require('../models/abdm-model')

class ABDMHandler {
  /**
   * Initiate ABHA number creation using Aadhaar
   * @param {Object} req - Request object
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByAadhaar(req) {
    try {
      const requestData = await req.json()

      // Validate request using model
      const validation = ABDMValidator.validateRequest(
        ABDMInitiateAadhaarRequest,
        requestData,
      )
      if (!validation.isValid) {
        return jsonResponse(
          ABDMValidator.formatValidationErrors(validation.errors),
          HttpStatusCode.BadRequest,
        )
      }

      const { aadhaar, mobile } = requestData
      console.log('fffffffffffff')

      logInfo(`Initiating ABHA creation by Aadhaar for mobile: ${mobile}`)

      const result = await abdmService.initiateAbhaCreationByAadhaar(
        aadhaar,
        mobile,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in initiateAbhaCreationByAadhaar handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Initiate ABHA number creation using Mobile
   * @param {Object} req - Request object
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByMobile(req) {
    try {
      const { mobile } = await req.json()

      // Validate required fields
      if (!mobile) {
        return jsonResponse(
          'Mobile number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate mobile format (10 digits)
      if (!/^\d{10}$/.test(mobile)) {
        return jsonResponse(
          'Invalid mobile number format. Must be 10 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Initiating ABHA creation by mobile: ${mobile}`)

      const result = await abdmService.initiateAbhaCreationByMobile(mobile)
      console.log(result, 'kkkkkkkkkkkkkkkkkk')

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId, // Include txnId in the response
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in initiateAbhaCreationByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP for ABHA creation
   * @param {Object} req - Request object
   * @returns {Object} - Response with verification status
   */
  async verifyOtpForAbhaCreation(req) {
    try {
      const { txnId, otp, type, mobile } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate type (mobile or aadhaar)
      if (type && !['mobile', 'aadhaar'].includes(type)) {
        return jsonResponse(
          'Invalid type. Must be "mobile" or "aadhaar".',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(
        `Verifying ${
          type || 'aadhaar'
        } OTP for ABHA creation with txnId: ${txnId}`,
      )

      // Use appropriate service method based on type
      const result =
        type === 'mobile'
          ? await abdmService.verifyMobileOtpForAbhaCreation(txnId, otp)
          : await abdmService.verifyOtpForAbhaCreation(txnId, otp, mobile)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
            data: result.data,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpForAbhaCreation handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Complete ABHA number creation
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA number
   */
  async completeAbhaCreation(req) {
    try {
      const { txnId, profileData, type } = await req.json()

      // Validate required fields
      if (!txnId || !profileData) {
        return jsonResponse(
          'Transaction ID and profile data are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate profile data
      const requiredFields = ['firstName', 'lastName', 'gender', 'yearOfBirth']
      for (const field of requiredFields) {
        if (!profileData[field]) {
          return jsonResponse(
            `${field} is required in profile data`,
            HttpStatusCode.BadRequest,
          )
        }
      }

      // Validate type (mobile or aadhaar)
      if (type && !['mobile', 'aadhaar'].includes(type)) {
        return jsonResponse(
          'Invalid type. Must be "mobile" or "aadhaar".',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(
        `Completing ${type || 'aadhaar'} ABHA creation with txnId: ${txnId}`,
      )

      // Use appropriate service method based on type
      const result =
        type === 'mobile'
          ? await abdmService.completeMobileAbhaCreation(txnId, profileData)
          : await abdmService.completeAbhaCreation(txnId, profileData)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            abhaNumber: result.abhaNumber,
            abhaAddress: result.abhaAddress,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in completeAbhaCreation handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Fetch ABHA details by ABHA number
   * @param {Object} req - Request object
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByNumber(req) {
    try {
      const { abhaNumber } = await req.json()

      // Validate required fields
      if (!abhaNumber) {
        return jsonResponse(
          'ABHA number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate ABHA number format (14 digits with hyphens or 14 digits)
      if (
        !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaNumber) &&
        !/^\d{14}$/.test(abhaNumber)
      ) {
        return jsonResponse(
          'Invalid ABHA number format',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Fetching ABHA details for number: ${abhaNumber}`)

      const result = await abdmService.getAbhaDetailsByNumber(abhaNumber)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in getAbhaDetailsByNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Fetch ABHA details by mobile number
   * @param {Object} req - Request object
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByMobile(req) {
    try {
      const { mobile } = await req.json()

      // Validate required fields
      if (!mobile) {
        return jsonResponse(
          'Mobile number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate mobile format (10 digits)
      if (!/^\d{10}$/.test(mobile)) {
        return jsonResponse(
          'Invalid mobile number format. Must be 10 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Fetching ABHA details for mobile: ${mobile}`)

      const result = await abdmService.getAbhaDetailsByMobile(mobile)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in getAbhaDetailsByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify ABHA number
   * @param {Object} req - Request object
   * @returns {Object} - Verification result
   */
  async verifyAbhaNumber(req) {
    try {
      const { abhaNumber } = await req.json()

      // Validate required fields
      if (!abhaNumber) {
        return jsonResponse(
          'ABHA number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate ABHA number format (14 digits with hyphens or 14 digits)
      if (
        !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaNumber) &&
        !/^\d{14}$/.test(abhaNumber)
      ) {
        return jsonResponse(
          'Invalid ABHA number format',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying ABHA number: ${abhaNumber}`)

      const result = await abdmService.verifyAbhaNumber(abhaNumber)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            isValid: result.isValid,
            status: result.status,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            isValid: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyAbhaNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Resend OTP for ABHA operations
   * @param {Object} req - Request object
   * @returns {Object} - Response
   */
  async resendOtp(req) {
    try {
      const { txnId } = await req.json()

      // Validate required fields
      if (!txnId) {
        return jsonResponse(
          'Transaction ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Resending OTP for txnId: ${txnId}`)

      const result = await abdmService.resendOtp(txnId)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in resendOtp handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and create ABHA number using Aadhaar
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA number
   */
  async verifyOtpAndCreateAbhaByAadhaar(req) {
    try {
      const { txnId, otp, mobile } = await req.json()

      // Validate required fields
      if (!txnId || !otp || !mobile) {
        return jsonResponse(
          'Transaction ID, OTP, and mobile number are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate mobile format (10 digits)
      if (!/^\d{10}$/.test(mobile)) {
        return jsonResponse(
          'Invalid mobile number format. Must be 10 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(
        `Verifying OTP and creating ABHA number using Aadhaar with txnId: ${txnId}`,
      )

      const result = await abdmService.verifyOtpAndCreateAbhaByAadhaar(
        txnId,
        otp,
        mobile,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            abhaNumber: result.abhaNumber,
            abhaAddress: result.abhaAddress,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndCreateAbhaByAadhaar handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and fetch ABHA details by number
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByNumber(req) {
    try {
      const { txnId, otp } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying OTP and fetching ABHA details for txnId: ${txnId}`)

      const result = await abdmService.verifyOtpAndFetchAbhaDetailsByNumber(
        txnId,
        otp,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndFetchAbhaDetailsByNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and fetch ABHA details by mobile number
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByMobile(req) {
    try {
      const { txnId, otp } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying OTP and fetching ABHA details for txnId: ${txnId}`)

      const result = await abdmService.verifyAbhaDetailsByMobile(
        txnId,
        otp,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndFetchAbhaDetailsByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new ABDMHandler()
