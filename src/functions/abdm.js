/**
 * Azure Functions for ABDM (ABHA) operations
 * Handles ABHA number generation, verification, and management
 */

const { app } = require('@azure/functions')
const abdmHandler = require('../handlers/abdm-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// ABHA Number Generation - Initiate by Aadhaar
app.http('abdm-initiate-aadhaar', {
  methods: ['POST'],
  route: 'abdm/initiate/aadhaar',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Initiating ABHA creation by <PERSON>ad<PERSON><PERSON>')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.initiateAbhaCreationByAadhaar(req)
  },
})

// ABHA Number Generation - Initiate by Mobile
app.http('abdm-initiate-mobile', {
  methods: ['POST'],
  route: 'abdm/initiate/mobile',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Initiating ABHA creation by Mobile')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.initiateAbhaCreationByMobile(req)
  },
})

// ABHA Number Generation - Verify OTP
app.http('abdm-verify-otp', {
  methods: ['POST'],
  route: 'abdm/verify-otp',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Verifying OTP for ABHA creation')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.verifyOtpForAbhaCreation(req)
  },
})

// ABHA Number Generation - Complete Creation
app.http('abdm-complete-creation', {
  methods: ['POST'],
  route: 'abdm/complete-creation',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Completing ABHA creation')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.completeAbhaCreation(req)
  },
})

// ABHA Details - Get by ABHA Number
app.http('abdm-details-by-number', {
  methods: ['POST'],
  route: 'abdm/details/by-number',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Fetching ABHA details by number')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.getAbhaDetailsByNumber(req)
  },
})

// ABHA Details - Get by Mobile Number
app.http('abdm-details-by-mobile', {
  methods: ['POST'],
  route: 'abdm/details/by-mobile',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Fetching ABHA details by mobile')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.getAbhaDetailsByMobile(req)
  },
})

// ABHA Number Verification
app.http('abdm-verify-number', {
  methods: ['POST'],
  route: 'abdm/verify-number',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Verifying ABHA number')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.verifyAbhaNumber(req)
  },
})

// Resend OTP
app.http('abdm-resend-otp', {
  methods: ['POST'],
  route: 'abdm/resend-otp',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Resending OTP')
    
    if (!req.body) {
      return jsonResponse(
        'Missing request payload',
        HttpStatusCode.BadRequest,
      )
    }

    return await abdmHandler.resendOtp(req)
  },
})

// Combined ABDM endpoint for multiple operations (following existing pattern)
app.http('abdm', {
  methods: ['GET', 'POST'],
  route: 'abdm',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`ABDM function processed request for url "${req.url}"`)
    
    const operation = req.query.get('operation')
    
    switch (req.method) {
      case HttpMethod.get:
        // GET operations for status checks or information
        if (operation === 'health') {
          return jsonResponse({
            status: 'healthy',
            service: 'ABDM Integration',
            version: '1.0.0',
            timestamp: new Date().toISOString()
          })
        }
        
        return jsonResponse(
          'Invalid operation. Use operation=health for health check.',
          HttpStatusCode.BadRequest,
        )

      case HttpMethod.post:
        if (!req.body) {
          return jsonResponse(
            'Missing request payload',
            HttpStatusCode.BadRequest,
          )
        }

        // Route based on operation parameter
        switch (operation) {
          case 'initiate-aadhaar':
            return await abdmHandler.initiateAbhaCreationByAadhaar(req)
          
          case 'initiate-mobile':
            return await abdmHandler.initiateAbhaCreationByMobile(req)
          
          case 'verify-otp':
            return await abdmHandler.verifyOtpForAbhaCreation(req)
          
          case 'complete-creation':
            return await abdmHandler.completeAbhaCreation(req)
          
          case 'details-by-number':
            return await abdmHandler.getAbhaDetailsByNumber(req)
          
          case 'details-by-mobile':
            return await abdmHandler.getAbhaDetailsByMobile(req)
          
          case 'verify-number':
            return await abdmHandler.verifyAbhaNumber(req)
          
          case 'resend-otp':
            return await abdmHandler.resendOtp(req)
          
          default:
            return jsonResponse(
              'Invalid operation. Supported operations: initiate-aadhaar, initiate-mobile, verify-otp, complete-creation, details-by-number, details-by-mobile, verify-number, resend-otp',
              HttpStatusCode.BadRequest,
            )
        }

      default:
        return jsonResponse(
          'Method not allowed',
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
