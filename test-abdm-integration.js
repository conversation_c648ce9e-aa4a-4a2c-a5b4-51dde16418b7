/**
 * ABDM Integration Test Script
 * Tests all ABDM endpoints and validates the complete flow
 */

const axios = require('axios')

// Configuration
const BASE_URL = 'http://localhost:7071/api' // Local development URL
const TEST_CONFIG = {
  aadhaar: '123456789012', // Test Aadhaar number
  mobile: '9876543210',    // Test mobile number
  abhaNumber: '12-**************', // Test ABHA number for verification
  otp: '123456' // Test OTP (will be provided by ABDM in real scenario)
}

class ABDMIntegrationTest {
  constructor() {
    this.testResults = []
    this.txnId = null
  }

  /**
   * Log test result
   */
  logResult(testName, success, message, data = null) {
    const result = {
      test: testName,
      success,
      message,
      timestamp: new Date().toISOString(),
      data
    }
    this.testResults.push(result)
    console.log(`${success ? '✅' : '❌'} ${testName}: ${message}`)
    if (data) {
      console.log('   Data:', JSON.stringify(data, null, 2))
    }
  }

  /**
   * Make HTTP request to ABDM endpoint
   */
  async makeRequest(endpoint, data, method = 'POST') {
    try {
      const config = {
        method,
        url: `${BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json'
        }
      }

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = data
      }

      const response = await axios(config)
      return {
        success: true,
        data: response.data,
        status: response.status
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        status: error.response?.status
      }
    }
  }

  /**
   * Test health check endpoint
   */
  async testHealthCheck() {
    const result = await this.makeRequest('/abdm?operation=health', null, 'GET')
    
    if (result.success && result.data.status === 'healthy') {
      this.logResult('Health Check', true, 'ABDM service is healthy', result.data)
    } else {
      this.logResult('Health Check', false, 'ABDM service health check failed', result.error)
    }
  }

  /**
   * Test ABHA creation initiation by Aadhaar
   */
  async testInitiateByAadhaar() {
    const requestData = {
      aadhaar: TEST_CONFIG.aadhaar,
      mobile: TEST_CONFIG.mobile
    }

    const result = await this.makeRequest('/abdm/initiate/aadhaar', requestData)
    
    if (result.success && result.data.success && result.data.txnId) {
      this.txnId = result.data.txnId
      this.logResult('Initiate by Aadhaar', true, 'ABHA creation initiated successfully', result.data)
    } else {
      this.logResult('Initiate by Aadhaar', false, 'Failed to initiate ABHA creation by Aadhaar', result.error)
    }
  }

  /**
   * Test ABHA creation initiation by Mobile
   */
  async testInitiateByMobile() {
    const requestData = {
      mobile: TEST_CONFIG.mobile
    }

    const result = await this.makeRequest('/abdm/initiate/mobile', requestData)
    
    if (result.success && result.data.success && result.data.txnId) {
      // Update txnId if this test is run independently
      if (!this.txnId) {
        this.txnId = result.data.txnId
      }
      this.logResult('Initiate by Mobile', true, 'ABHA creation initiated successfully', result.data)
    } else {
      this.logResult('Initiate by Mobile', false, 'Failed to initiate ABHA creation by Mobile', result.error)
    }
  }

  /**
   * Test OTP verification (requires valid txnId from previous step)
   */
  async testVerifyOtp() {
    if (!this.txnId) {
      this.logResult('Verify OTP', false, 'No transaction ID available from previous steps')
      return
    }

    const requestData = {
      txnId: this.txnId,
      otp: TEST_CONFIG.otp
    }

    const result = await this.makeRequest('/abdm/verify-otp', requestData)
    
    if (result.success && result.data.success) {
      this.logResult('Verify OTP', true, 'OTP verified successfully', result.data)
    } else {
      this.logResult('Verify OTP', false, 'Failed to verify OTP', result.error)
    }
  }

  /**
   * Test ABHA creation completion
   */
  async testCompleteCreation() {
    if (!this.txnId) {
      this.logResult('Complete Creation', false, 'No transaction ID available from previous steps')
      return
    }

    const requestData = {
      txnId: this.txnId,
      profileData: {
        firstName: 'John',
        lastName: 'Doe',
        gender: 'M',
        yearOfBirth: '1990',
        email: '<EMAIL>'
      }
    }

    const result = await this.makeRequest('/abdm/complete-creation', requestData)
    
    if (result.success && result.data.success && result.data.abhaNumber) {
      this.logResult('Complete Creation', true, 'ABHA number created successfully', result.data)
    } else {
      this.logResult('Complete Creation', false, 'Failed to complete ABHA creation', result.error)
    }
  }

  /**
   * Test ABHA details fetching by number
   */
  async testGetDetailsByNumber() {
    const requestData = {
      abhaNumber: TEST_CONFIG.abhaNumber
    }

    const result = await this.makeRequest('/abdm/details/by-number', requestData)
    
    if (result.success && result.data.success) {
      this.logResult('Get Details by Number', true, 'ABHA details retrieved successfully', result.data)
    } else {
      this.logResult('Get Details by Number', false, 'Failed to get ABHA details by number', result.error)
    }
  }

  /**
   * Test ABHA details fetching by mobile
   */
  async testGetDetailsByMobile() {
    const requestData = {
      mobile: TEST_CONFIG.mobile
    }

    const result = await this.makeRequest('/abdm/details/by-mobile', requestData)
    
    if (result.success && result.data.success) {
      this.logResult('Get Details by Mobile', true, 'ABHA details retrieved successfully', result.data)
    } else {
      this.logResult('Get Details by Mobile', false, 'Failed to get ABHA details by mobile', result.error)
    }
  }

  /**
   * Test ABHA number verification
   */
  async testVerifyNumber() {
    const requestData = {
      abhaNumber: TEST_CONFIG.abhaNumber
    }

    const result = await this.makeRequest('/abdm/verify-number', requestData)
    
    if (result.success && result.data.success) {
      this.logResult('Verify Number', true, 'ABHA number verified successfully', result.data)
    } else {
      this.logResult('Verify Number', false, 'Failed to verify ABHA number', result.error)
    }
  }

  /**
   * Test OTP resend functionality
   */
  async testResendOtp() {
    if (!this.txnId) {
      this.logResult('Resend OTP', false, 'No transaction ID available from previous steps')
      return
    }

    const requestData = {
      txnId: this.txnId
    }

    const result = await this.makeRequest('/abdm/resend-otp', requestData)
    
    if (result.success && result.data.success) {
      this.logResult('Resend OTP', true, 'OTP resent successfully', result.data)
    } else {
      this.logResult('Resend OTP', false, 'Failed to resend OTP', result.error)
    }
  }

  /**
   * Test validation errors
   */
  async testValidationErrors() {
    // Test invalid Aadhaar
    const invalidAadhaarResult = await this.makeRequest('/abdm/initiate/aadhaar', {
      aadhaar: '123', // Invalid format
      mobile: TEST_CONFIG.mobile
    })

    if (!invalidAadhaarResult.success || !invalidAadhaarResult.data.success) {
      this.logResult('Validation - Invalid Aadhaar', true, 'Validation correctly rejected invalid Aadhaar')
    } else {
      this.logResult('Validation - Invalid Aadhaar', false, 'Validation failed to reject invalid Aadhaar')
    }

    // Test invalid mobile
    const invalidMobileResult = await this.makeRequest('/abdm/initiate/mobile', {
      mobile: '123' // Invalid format
    })

    if (!invalidMobileResult.success || !invalidMobileResult.data.success) {
      this.logResult('Validation - Invalid Mobile', true, 'Validation correctly rejected invalid mobile')
    } else {
      this.logResult('Validation - Invalid Mobile', false, 'Validation failed to reject invalid mobile')
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting ABDM Integration Tests...\n')

    // Basic tests
    await this.testHealthCheck()
    await this.testValidationErrors()

    // ABHA creation flow tests
    await this.testInitiateByAadhaar()
    await this.testInitiateByMobile()
    
    // Note: OTP verification and completion require real OTP from ABDM
    // These tests will likely fail in sandbox without proper OTP
    console.log('\n⚠️  Note: OTP-related tests require real OTP from ABDM sandbox')
    await this.testVerifyOtp()
    await this.testCompleteCreation()
    await this.testResendOtp()

    // ABHA details and verification tests
    await this.testGetDetailsByNumber()
    await this.testGetDetailsByMobile()
    await this.testVerifyNumber()

    // Print summary
    this.printSummary()
  }

  /**
   * Print test summary
   */
  printSummary() {
    console.log('\n📊 Test Summary:')
    console.log('================')
    
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.success).length
    const failedTests = totalTests - passedTests

    console.log(`Total Tests: ${totalTests}`)
    console.log(`Passed: ${passedTests}`)
    console.log(`Failed: ${failedTests}`)
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(2)}%`)

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:')
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`))
    }

    console.log('\n✅ ABDM Integration Testing Complete!')
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new ABDMIntegrationTest()
  tester.runAllTests().catch(error => {
    console.error('❌ Test execution failed:', error)
    process.exit(1)
  })
}

module.exports = ABDMIntegrationTest
